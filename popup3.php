<div class="modal micromodal-slide" id="modal-1" aria-hidden="false">
    <div class="modal__overlay" tabindex="-1" data-micromodal-close>
        <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="">
            <header class="modal__header" style="height: 20px;">
                <h2 class="modal__title" id="modal-1-title">
                </h2>
                <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
            </header>
            <main class="modal__content" id="modal-1-content">
				<h3 style="font-weight: bold;">
                    20<sup>th</sup> International Strategic Managemenet Conference Program <br> is published on our website
                </h3>
				 <a style="background-color: #18ADB5; color: white;" class="btn btn-primary" href="?p=conference-programme">For details, please visit </a>

            </main>
            <footer class="modal__footer text-center">
            </footer>
        </div>
    </div>
</div>
<script src="https://unpkg.com/micromodal/dist/micromodal.min.js"></script>
<script>
    MicroModal.show('modal-1',
        {
            onShow: modal => console.info(`${modal.id} is shown`), // [1]
            onClose: modal => $('#modal-1-content').html(''), // [2]
            openClass: 'is-open', // [5]
            disableScroll: true, // [6]
            disableFocus: false, // [7]
            awaitOpenAnimation: false, // [8]
            awaitCloseAnimation: false, // [9]
            debugMode: true // [10]
        }
    );
</script>
<link rel="stylesheet" href="css/modal.css?v=4">
<style>
    main.modal__content h3{
        padding-right: 15px;
        padding-left: 15px;
    }
    @media screen and (min-width: 1024px ) {
        main.modal__content{
            min-width: 750px;
        }
    }
</style>