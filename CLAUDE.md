# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the ISMC2025 (20th International Strategic Management Conference) website - a PHP-based conference website that will be held in Belgrade, Serbia at The Institute of Economic Sciences, between September 11-13, 2025.

## Technology Stack

- **Backend**: PHP (no framework, vanilla PHP)
- **Frontend**: HTML, CSS (Bootstrap), JavaScript (jQuery, Alpine.js)
- **Database**: Not identified in codebase (likely MySQL/MariaDB based on PHP usage)
- **No build tools**: No package.json, composer.json, or build system detected

## Project Structure

```
/
├── *.php                    # Main PHP pages (about-conference, accommodation, etc.)
├── css/                     # Stylesheets (Bootstrap, custom.css, fonts)
├── js/                      # JavaScript files (jQuery, Bootstrap, custom scripts)
├── img/                     # Images and graphics
├── photos/                  # Conference photos and thumbnails
├── doc/                     # Documentation and conference materials
└── uploads/                 # User upload directory (for submissions)
```

## Key Architecture Components

### Page Routing
- Uses query parameter routing: `?p=page-name` (e.g., `?p=about-conference`)
- Main entry point is `index.php`
- Common components: `header.php`, `footer.php`, `menu.php`

### Form Submissions
- Online submission form in `submission.php` uses Alpine.js for interactivity
- Submits to API endpoint: `/api/v1/save-doc-form-content`
- File uploads handled for document submissions

### Styling
- Bootstrap CSS framework
- Custom styles in `css/custom.css`
- Responsive design with mobile support

## Development Commands

Since this is a vanilla PHP project with no build system:

### Running the Project
```bash
# Using PHP's built-in server (development only)
php -S localhost:8000

# Or configure with Apache/Nginx for production
```

### Version Control
```bash
# Check current changes
git status

# View uncommitted changes
git diff
```

## Important Notes

- The project uses `$alt_klasor_adi = 'ismc2025'` for subdirectory configuration
- Error reporting is set to show only errors, warnings, and parse errors
- The site includes Google reCAPTCHA integration
- Multilingual support with Turkish content (anasayfa.php)
- Conference dates: September 11-13, 2025
- Venue: Institute of Economic Sciences, Belgrade, Serbia

## Current State

Based on git status:
- Modified files: `conference-venue.php`, `css/custom.css`
- New untracked files: `doc/info-pack.md`, `doc/restaurants-and-health-information-2025.pdf`