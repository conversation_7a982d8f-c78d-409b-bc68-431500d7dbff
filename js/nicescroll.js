/* jquery.nicescroll 3.1.0 InuYaksa*2012 MIT http://areaaperta.com/nicescroll */(function(e){var r=false,w=false,B=5E3,C=2E3,D=function(){var e=document.getElementsByTagName("script"),e=e[e.length-1].src.split("?")[0];return e.split("/").length>0?e.split("/").slice(0,-1).join("/")+"/":""}(),p=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||false,q=window.cancelRequestAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||
	window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||false,x=false,E=function(){if(x)return x;var e=document.createElement("DIV"),c={haspointerlock:"pointerLockElement"in document||"mozPointerLockElement"in document||"webkitPointerLockElement"in document};c.isopera="opera"in window;c.isopera12=c.isopera&&"getUserMedia"in navigator;c.isie="all"in document&&"attachEvent"in e&&!c.isopera;c.isieold=c.isie&&!("msInterpolationMode"in e.style);c.isie7=c.isie&&!c.isieold&&(!("documentMode"in
	document)||document.documentMode==7);c.isie8=c.isie&&"documentMode"in document&&document.documentMode==8;c.isie9=c.isie&&"performance"in window&&document.documentMode>=9;c.isie10=c.isie&&"performance"in window&&document.documentMode>=10;c.isie9mobile=/iemobile.9/i.test(navigator.userAgent);if(c.isie9mobile)c.isie9=false;c.isie7mobile=!c.isie9mobile&&c.isie7&&/iemobile/i.test(navigator.userAgent);c.ismozilla="MozAppearance"in e.style;c.iswebkit="WebkitAppearance"in e.style;c.ischrome="chrome"in window;
	c.ischrome22=c.ischrome&&c.haspointerlock;c.cantouch="ontouchstart"in document.documentElement||"ontouchstart"in window;c.hasmstouch=window.navigator.msPointerEnabled||false;c.ismac=/^mac$/i.test(navigator.platform);c.isios=c.cantouch&&/iphone|ipad|ipod/i.test(navigator.platform);c.isios4=c.isios&&!("seal"in Object);c.isandroid=/android/i.test(navigator.userAgent);c.trstyle=false;c.hastransform=false;c.hastranslate3d=false;c.transitionstyle=false;c.hastransition=false;c.transitionend=false;for(var h=
		["transform","msTransform","webkitTransform","MozTransform","OTransform"],i=0;i<h.length;i++)if(typeof e.style[h[i]]!="undefined"){c.trstyle=h[i];break}c.hastransform=c.trstyle!=false;if(c.hastransform)e.style[c.trstyle]="translate3d(1px,2px,3px)",c.hastranslate3d=/translate3d/.test(e.style[c.trstyle]);c.transitionstyle=false;c.prefixstyle="";c.transitionend=false;for(var h="transition,webkitTransition,MozTransition,OTransition,OTransition,msTransition,KhtmlTransition".split(","),b=",-webkit-,-moz-,-o-,-o,-ms-,-khtml-".split(","),
			                                                                                                                                                                                                                                                                                                                                                                                   n="transitionend,webkitTransitionEnd,transitionend,otransitionend,oTransitionEnd,msTransitionEnd,KhtmlTransitionEnd".split(","),i=0;i<h.length;i++)if(h[i]in e.style){c.transitionstyle=h[i];c.prefixstyle=b[i];c.transitionend=n[i];break}c.hastransition=c.transitionstyle;a:{h=["-moz-grab","-webkit-grab","grab"];if(c.ischrome&&!c.ischrome22||c.isie)h=[];for(i=0;i<h.length;i++)if(b=h[i],e.style.cursor=b,e.style.cursor==b){h=b;break a}h="url(http://www.google.com/intl/en_ALL/mapfiles/openhand.cur),n-resize"}c.cursorgrabvalue=
		h;c.hasmousecapture="setCapture"in e;return x=c},F=function(j,c){function h(d,c,f){c=d.css(c);d=parseFloat(c);return isNaN(d)?(d=o[c]||0,f=d==3?f?b.win.outerHeight()-b.win.innerHeight():b.win.outerWidth()-b.win.innerWidth():1,b.isie8&&d&&(d+=1),f?d:0):d}function i(d,c){var f=0,g=0,e=1;"wheelDeltaY"in d?(e=b.opt.mousescrollstep/48,f=Math.floor(d.wheelDeltaX*e),g=Math.floor(d.wheelDeltaY*e)):(e=d.detail?d.detail*-1:d.wheelDelta/40)&&(c?f=Math.floor(e*b.opt.mousescrollstep):g=Math.floor(e*b.opt.mousescrollstep));
	f&&(b.scrollmom&&b.scrollmom.stop(),b.lastdeltax+=f,b.synched("mousewheelx",function(){var d=b.lastdeltax;b.lastdeltax=0;b.rail.drag||b.doScrollLeftBy(d)}));g&&(b.scrollmom&&b.scrollmom.stop(),b.lastdeltay+=g,b.synched("mousewheely",function(){var d=b.lastdeltay;b.lastdeltay=0;b.rail.drag||b.doScrollBy(d)}))}var b=this;this.version="3.1.0";this.name="nicescroll";this.me=c;this.opt={doc:e("body"),win:false,zindex:9E3,cursoropacitymin:0,cursoropacitymax:1,cursorcolor:"#424242",cursorwidth:"5px",cursorborder:"1px solid #fff",
	cursorborderradius:"5px",scrollspeed:60,mousescrollstep:24,touchbehavior:false,hwacceleration:true,usetransition:true,boxzoom:false,dblclickzoom:true,gesturezoom:true,grabcursorenabled:true,autohidemode:true,background:"",iframeautoresize:true,cursorminheight:32,preservenativescrolling:true,railoffset:false,bouncescroll:true,spacebarenabled:true,railpadding:{top:0,right:0,left:0,bottom:0},disableoutline:true,horizrailenabled:true,railalign:"right",railvalign:"bottom",enabletranslate3d:true,enablemousewheel:true,
	enablekeyboard:true,smoothscroll:true,sensitiverail:true};this.opt.snapbackspeed=80;if(j)for(var n in b.opt)typeof j[n]!="undefined"&&(b.opt[n]=j[n]);this.iddoc=(this.doc=b.opt.doc)&&this.doc[0]?this.doc[0].id||"":"";this.ispage=/BODY|HTML/.test(b.opt.win?b.opt.win[0].nodeName:this.doc[0].nodeName);this.haswrapper=b.opt.win!==false;this.win=b.opt.win||(this.ispage?e(window):this.doc);this.docscroll=this.ispage&&!this.haswrapper?e(window):this.win;this.body=e("body");this.iframe=this.isfixed=this.viewport=
	false;this.isiframe=this.doc[0].nodeName=="IFRAME"&&this.win[0].nodeName=="IFRAME";this.istextarea=this.win[0].nodeName=="TEXTAREA";this.forcescreen=false;this.canshowonmouseevent=b.opt.autohidemode!="scroll";this.page=this.view=this.onzoomout=this.onzoomin=this.onscrollcancel=this.onscrollend=this.onscrollstart=this.onclick=this.ongesturezoom=this.onkeypress=this.onmousewheel=this.onmousemove=this.onmouseup=this.onmousedown=false;this.scroll={x:0,y:0};this.scrollratio={x:0,y:0};this.cursorheight=
	20;this.scrollvaluemax=0;this.observer=this.scrollmom=this.scrollrunning=false;do this.id="ascrail"+C++;while(document.getElementById(this.id));this.hasmousefocus=this.hasfocus=this.zoomactive=this.zoom=this.cursorfreezed=this.cursor=this.rail=false;this.visibility=true;this.hidden=this.locked=false;this.cursoractive=true;this.nativescrollingarea=false;this.events=[];this.saved={};this.delaylist={};this.synclist={};this.lastdeltay=this.lastdeltax=0;this.detected=E();var g=e.extend({},this.detected);
	this.ishwscroll=(this.canhwscroll=g.hastransform&&b.opt.hwacceleration)&&b.haswrapper;this.istouchcapable=false;if(g.cantouch&&g.ischrome&&!g.isios&&!g.isandroid)this.istouchcapable=true,g.cantouch=false;if(g.cantouch&&g.ismozilla&&!g.isios)this.istouchcapable=true,g.cantouch=false;this.delayed=function(d,c,f,g){var e=b.delaylist[d],h=(new Date).getTime();if(!g&&e&&e.tt)return false;e&&e.tt&&clearTimeout(e.tt);if(e&&e.last+f>h&&!e.tt)b.delaylist[d]={last:h+f,tt:setTimeout(function(){b.delaylist[d].tt=
		0;c.call()},f)};else if(!e||!e.tt)b.delaylist[d]={last:h,tt:0},setTimeout(function(){c.call()},0)};this.synched=function(d,c){b.synclist[d]=c;(function(){if(!b.onsync)p(function(){b.onsync=false;for(d in b.synclist){var c=b.synclist[d];c&&c.call(b);b.synclist[d]=false}}),b.onsync=true})();return d};this.unsynched=function(d){b.synclist[d]&&(b.synclist[d]=false)};this.css=function(d,c){for(var f in c)b.saved.css.push([d,f,d.css(f)]),d.css(f,c[f])};this.scrollTop=function(d){return typeof d=="undefined"?
		b.getScrollTop():b.setScrollTop(d)};this.scrollLeft=function(d){return typeof d=="undefined"?b.getScrollLeft():b.setScrollLeft(d)};BezierClass=function(b,c,f,g,e,h,i){this.st=b;this.ed=c;this.spd=f;this.p1=g||0;this.p2=e||1;this.p3=h||0;this.p4=i||1;this.ts=(new Date).getTime();this.df=this.ed-this.st};BezierClass.prototype={B2:function(b){return 3*b*b*(1-b)},B3:function(b){return 3*b*(1-b)*(1-b)},B4:function(b){return(1-b)*(1-b)*(1-b)},getNow:function(){var b=1-((new Date).getTime()-this.ts)/this.spd,
		c=this.B2(b)+this.B3(b)+this.B4(b);return b<0?this.ed:this.st+Math.round(this.df*c)},update:function(b,c){this.st=this.getNow();this.ed=b;this.spd=c;this.ts=(new Date).getTime();this.df=this.ed-this.st;return this}};if(this.ishwscroll){this.doc.translate={x:0,y:0,tx:"0px",ty:"0px"};g.hastranslate3d&&g.isios&&this.doc.css("-webkit-backface-visibility","hidden");var m=function(){var d=b.doc.css(g.trstyle);return d&&d.substr(0,6)=="matrix"?d.replace(/^.*\((.*)\)$/g,"$1").replace(/px/g,"").split(/, +/):
		false};this.getScrollTop=function(d){if(!d){if(d=m())return d.length==16?-d[13]:-d[5];if(b.timerscroll&&b.timerscroll.bz)return b.timerscroll.bz.getNow()}return b.doc.translate.y};this.getScrollLeft=function(d){if(!d){if(d=m())return d.length==16?-d[12]:-d[4];if(b.timerscroll&&b.timerscroll.bh)return b.timerscroll.bh.getNow()}return b.doc.translate.x};this.notifyScrollEvent=document.createEvent?function(b){var c=document.createEvent("UIEvents");c.initUIEvent("scroll",false,true,window,1);b.dispatchEvent(c)}:
		document.fireEvent?function(b){var c=document.createEventObject();b.fireEvent("onscroll");c.cancelBubble=true}:function(){};g.hastranslate3d&&b.opt.enabletranslate3d?(this.setScrollTop=function(d,c){b.doc.translate.y=d;b.doc.translate.ty=d*-1+"px";b.doc.css(g.trstyle,"translate3d("+b.doc.translate.tx+","+b.doc.translate.ty+",0px)");c||b.notifyScrollEvent(b.win[0])},this.setScrollLeft=function(d,c){b.doc.translate.x=d;b.doc.translate.tx=d*-1+"px";b.doc.css(g.trstyle,"translate3d("+b.doc.translate.tx+
		","+b.doc.translate.ty+",0px)");c||b.notifyScrollEvent(b.win[0])}):(this.setScrollTop=function(d,c){b.doc.translate.y=d;b.doc.translate.ty=d*-1+"px";b.doc.css(g.trstyle,"translate("+b.doc.translate.tx+","+b.doc.translate.ty+")");c||b.notifyScrollEvent(b.win[0])},this.setScrollLeft=function(d,c){b.doc.translate.x=d;b.doc.translate.tx=d*-1+"px";b.doc.css(g.trstyle,"translate("+b.doc.translate.tx+","+b.doc.translate.ty+")");c||b.notifyScrollEvent(b.win[0])})}else this.getScrollTop=function(){return b.docscroll.scrollTop()},
		this.setScrollTop=function(d){return b.docscroll.scrollTop(d)},this.getScrollLeft=function(){return b.docscroll.scrollLeft()},this.setScrollLeft=function(d){return b.docscroll.scrollLeft(d)};this.getTarget=function(b){return!b?false:b.target?b.target:b.srcElement?b.srcElement:false};this.hasParent=function(b,c){if(!b)return false;for(var f=b.target||b.srcElement||b||false;f&&f.id!=c;)f=f.parentNode||false;return f!==false};var o={thin:1,medium:3,thick:5};this.getOffset=function(){if(b.isfixed)return{top:parseFloat(b.win.css("top")),
		left:parseFloat(b.win.css("left"))};if(!b.viewport)return b.win.offset();var d=b.win.offset(),c=b.viewport.offset();return{top:d.top-c.top+b.viewport.scrollTop(),left:d.left-c.left+b.viewport.scrollLeft()}};this.updateScrollBar=function(d){if(b.ishwscroll)b.rail.css({height:b.win.innerHeight()}),b.railh&&b.railh.css({width:b.win.innerWidth()});else{var c=b.getOffset(),f=c.top,g=c.left;f+=h(b.win,"border-top-width",true);b.win.outerWidth();b.win.innerWidth();g+=b.rail.align?b.win.outerWidth()-h(b.win,
		"border-right-width")-b.rail.width:h(b.win,"border-left-width");var e=b.opt.railoffset;e&&(e.top&&(f+=e.top),b.rail.align&&e.left&&(g+=e.left));b.locked||b.rail.css({top:f,left:g,height:d?d.h:b.win.innerHeight()});b.zoom&&b.zoom.css({top:f+1,left:b.rail.align==1?g-20:g+b.rail.width+4});if(b.railh&&!b.locked)f=c.top,g=c.left,d=b.railh.align?f+h(b.win,"border-top-width",true)+b.win.innerHeight()-b.railh.height:f+h(b.win,"border-top-width",true),g+=h(b.win,"border-left-width"),b.railh.css({top:d,left:g,
		width:b.railh.width})}};this.doRailClick=function(d,c,f){var g;!(b.rail.drag&&b.rail.drag.pt!=1)&&!b.locked&&!b.rail.drag&&(b.cancelScroll(),b.cancelEvent(d),c?(c=f?b.doScrollLeft:b.doScrollTop,g=f?(d.pageX-b.railh.offset().left-b.cursorwidth/2)*b.scrollratio.x:(d.pageY-b.rail.offset().top-b.cursorheight/2)*b.scrollratio.y,c(g)):(c=f?b.doScrollLeftBy:b.doScrollBy,g=f?b.scroll.x:b.scroll.y,d=f?d.pageX-b.railh.offset().left:d.pageY-b.rail.offset().top,f=f?b.view.w:b.view.h,g>=d?c(f):c(-f)))};b.hasanimationframe=
		p;b.hascancelanimationframe=q;b.hasanimationframe?b.hascancelanimationframe||(q=function(){b.cancelAnimationFrame=true}):(p=function(b){return setTimeout(b,16)},q=clearInterval);this.init=function(){b.saved.css=[];if(g.isie7mobile)return true;g.hasmstouch&&b.css(b.ispage?e("html"):b.win,{"-ms-touch-action":"none"});if(!b.ispage||!g.cantouch&&!g.isieold&&!g.isie9mobile){var d=b.docscroll;b.ispage&&(d=b.haswrapper?b.win:b.doc);g.isie9mobile||b.css(d,{"overflow-y":"hidden"});b.ispage&&g.isie7&&(b.doc[0].nodeName==
		"BODY"?b.css(e("html"),{"overflow-y":"hidden"}):b.doc[0].nodeName=="HTML"&&b.css(e("body"),{"overflow-y":"hidden"}));g.isios&&!b.ispage&&!b.haswrapper&&b.css(e("body"),{"-webkit-overflow-scrolling":"touch"});var c=e(document.createElement("div"));c.css({position:"relative",top:0,"float":"right",width:b.opt.cursorwidth,height:"0px","background-color":b.opt.cursorcolor,border:b.opt.cursorborder,"background-clip":"padding-box","-webkit-border-radius":b.opt.cursorborderradius,"-moz-border-radius":b.opt.cursorborderradius,
		"border-radius":b.opt.cursorborderradius});c.hborder=parseFloat(c.outerHeight()-c.innerHeight());b.cursor=c;var f=e(document.createElement("div"));f.attr("id",b.id);var h,i,j=["left","right"],y;for(y in j)i=j[y],(h=b.opt.railpadding[i])?f.css("padding-"+i,h+"px"):b.opt.railpadding[i]=0;f.append(c);f.width=Math.max(parseFloat(b.opt.cursorwidth),c.outerWidth())+b.opt.railpadding.left+b.opt.railpadding.right;f.css({width:f.width+"px",zIndex:b.ispage?b.opt.zindex:b.opt.zindex+2,background:b.opt.background});
		f.visibility=true;f.scrollable=true;f.align=b.opt.railalign=="left"?0:1;b.rail=f;c=b.rail.drag=false;if(b.opt.boxzoom&&!b.ispage&&!g.isieold&&(c=document.createElement("div"),b.bind(c,"click",b.doZoom),b.zoom=e(c),b.zoom.css({cursor:"pointer","z-index":b.opt.zindex,backgroundImage:"url("+D+"zoomico.png)",height:18,width:18,backgroundPosition:"0px 0px"}),b.opt.dblclickzoom&&b.bind(b.win,"dblclick",b.doZoom),g.cantouch&&b.opt.gesturezoom))b.ongesturezoom=function(d){d.scale>1.5&&b.doZoomIn(d);d.scale<
			0.8&&b.doZoomOut(d);return b.cancelEvent(d)},b.bind(b.win,"gestureend",b.ongesturezoom);b.railh=false;if(b.opt.horizrailenabled){b.css(d,{"overflow-x":"hidden"});c=e(document.createElement("div"));c.css({position:"relative",top:0,height:b.opt.cursorwidth,width:"0px","background-color":b.opt.cursorcolor,border:b.opt.cursorborder,"background-clip":"padding-box","-webkit-border-radius":b.opt.cursorborderradius,"-moz-border-radius":b.opt.cursorborderradius,"border-radius":b.opt.cursorborderradius});c.wborder=
			parseFloat(c.outerWidth()-c.innerWidth());b.cursorh=c;var k=e(document.createElement("div"));k.attr("id",b.id+"-hr");k.height=1+Math.max(parseFloat(b.opt.cursorwidth),c.outerHeight());k.css({height:k.height+"px",zIndex:b.ispage?b.opt.zindex:b.opt.zindex+2,background:b.opt.background});k.append(c);k.visibility=true;k.scrollable=true;k.align=b.opt.railvalign=="top"?0:1;b.railh=k;b.railh.drag=false}if(b.ispage)f.css({position:"fixed",top:"0px",height:"100%"}),f.align?f.css({right:"0px"}):f.css({left:"0px"}),
			b.body.append(f),b.railh&&(k.css({position:"fixed",left:"0px",width:"100%"}),k.align?k.css({bottom:"0px"}):k.css({top:"0px"}),b.body.append(k));else{if(b.ishwscroll)b.win.css("position")=="static"&&b.css(b.win,{position:"relative"}),d=b.win[0].nodeName=="HTML"?b.body:b.win,b.zoom&&(b.zoom.css({position:"absolute",top:1,right:0,"margin-right":f.width+4}),d.append(b.zoom)),f.css({position:"absolute",top:0}),f.align?f.css({right:0}):f.css({left:0}),d.append(f),k&&(k.css({position:"absolute",left:0,bottom:0}),
			k.align?k.css({bottom:0}):k.css({top:0}),d.append(k));else{b.isfixed=b.win.css("position")=="fixed";d=b.isfixed?"fixed":"absolute";if(!b.isfixed)b.viewport=b.getViewport(b.win[0]);if(b.viewport)b.body=b.viewport;f.css({position:d});b.zoom&&b.zoom.css({position:d});b.updateScrollBar();b.body.append(f);b.zoom&&b.body.append(b.zoom);b.railh&&(k.css({position:d}),b.body.append(k))}g.isios&&b.css(b.win,{"-webkit-tap-highlight-color":"rgba(0,0,0,0)","-webkit-touch-callout":"none"});g.isie&&b.opt.disableoutline&&
		b.win.attr("hideFocus","true");g.iswebkit&&b.opt.disableoutline&&b.win.css({outline:"none"})}if(b.opt.autohidemode===false)b.autohidedom=false;else if(b.opt.autohidemode===true){if(b.autohidedom=e().add(b.rail),b.railh)b.autohidedom=b.autohidedom.add(b.railh)}else if(b.opt.autohidemode=="scroll"){if(b.autohidedom=e().add(b.rail),b.railh)b.autohidedom=b.autohidedom.add(b.railh)}else if(b.opt.autohidemode=="cursor"){if(b.autohidedom=e().add(b.cursor),b.railh)b.autohidedom=b.autohidedom.add(b.railh.cursor)}else if(b.opt.autohidemode==
			"hidden")b.autohidedom=false,b.hide(),b.locked=false;if(g.isie9mobile)b.scrollmom=new z(b),b.onmangotouch=function(){var d=b.getScrollTop(),c=b.getScrollLeft();if(d==b.scrollmom.lastscrolly&&c==b.scrollmom.lastscrollx)return true;var f=d-b.mangotouch.sy,l=c-b.mangotouch.sx;if(Math.round(Math.sqrt(Math.pow(l,2)+Math.pow(f,2)))!=0){var g=f<0?-1:1,e=l<0?-1:1,h=+new Date;b.mangotouch.lazy&&clearTimeout(b.mangotouch.lazy);if(h-b.mangotouch.tm>80||b.mangotouch.dry!=g||b.mangotouch.drx!=e)b.scrollmom.stop(),
			b.scrollmom.reset(c,d),b.mangotouch.sy=d,b.mangotouch.ly=d,b.mangotouch.sx=c,b.mangotouch.lx=c,b.mangotouch.dry=g,b.mangotouch.drx=e,b.mangotouch.tm=h;else if(b.scrollmom.stop(),b.scrollmom.update(b.mangotouch.sx-l,b.mangotouch.sy-f),b.mangotouch.tm=h,f=Math.max(Math.abs(b.mangotouch.ly-d),Math.abs(b.mangotouch.lx-c)),b.mangotouch.ly=d,b.mangotouch.lx=c,f>2)b.mangotouch.lazy=setTimeout(function(){b.mangotouch.lazy=false;b.mangotouch.dry=0;b.mangotouch.drx=0;b.mangotouch.tm=0;b.scrollmom.doMomentum(30)},
			100)}},f=b.getScrollTop(),k=b.getScrollLeft(),b.mangotouch={sy:f,ly:f,dry:0,sx:k,lx:k,drx:0,lazy:false,tm:0},b.bind(b.docscroll,"scroll",b.onmangotouch);else{if(g.cantouch||b.istouchcapable||b.opt.touchbehavior||g.hasmstouch){b.scrollmom=new z(b);b.ontouchstart=function(d){if(d.pointerType&&d.pointerType!=2)return false;if(!b.locked){if(g.hasmstouch)for(var c=d.target?d.target:false;c;){var f=e(c).getNiceScroll();if(f.length>0&&f[0].me==b.me)break;if(f.length>0)return false;if(c.nodeName=="DIV"&&
			c.id==b.id)break;c=c.parentNode?c.parentNode:false}b.cancelScroll();if((c=b.getTarget(d))&&/INPUT/i.test(c.nodeName)&&/range/i.test(c.type))return b.stopPropagation(d);if(!("clientX"in d)&&"changedTouches"in d)d.clientX=d.changedTouches[0].clientX,d.clientY=d.changedTouches[0].clientY;if(b.forcescreen)f=d,d={original:d.original?d.original:d},d.clientX=f.screenX,d.clientY=f.screenY;b.rail.drag={x:d.clientX,y:d.clientY,sx:b.scroll.x,sy:b.scroll.y,st:b.getScrollTop(),sl:b.getScrollLeft(),pt:2};b.opt.touchbehavior&&
			b.isiframe&&g.isie&&(f=b.win.position(),b.rail.drag.x+=f.left,b.rail.drag.y+=f.top);b.hasmoving=false;b.lastmouseup=false;b.scrollmom.reset(d.clientX,d.clientY);if(!g.cantouch&&!this.istouchcapable&&!g.hasmstouch){if(!c||!/INPUT|SELECT|TEXTAREA/i.test(c.nodeName))return!b.ispage&&g.hasmousecapture&&c.setCapture(),b.cancelEvent(d);if(/SUBMIT|CANCEL|BUTTON/i.test(e(c).attr("type")))pc={tg:c,click:false},b.preventclick=pc}}};b.ontouchend=function(d){if(d.pointerType&&d.pointerType!=2)return false;if(b.rail.drag&&
			b.rail.drag.pt==2&&(b.scrollmom.doMomentum(),b.rail.drag=false,b.hasmoving&&(b.hasmoving=false,b.lastmouseup=true,b.hideCursor(),g.hasmousecapture&&document.releaseCapture(),!g.cantouch)))return b.cancelEvent(d)};var n=b.opt.touchbehavior&&b.isiframe&&!g.hasmousecapture;b.ontouchmove=function(d,c){if(d.pointerType&&d.pointerType!=2)return false;if(b.rail.drag&&b.rail.drag.pt==2){if(g.cantouch&&typeof d.original=="undefined")return true;b.hasmoving=true;if(b.preventclick&&!b.preventclick.click)b.preventclick.click=
			b.preventclick.tg.onclick||false,b.preventclick.tg.onclick=b.onpreventclick;d=e.extend({original:d},d);if("changedTouches"in d)d.clientX=d.changedTouches[0].clientX,d.clientY=d.changedTouches[0].clientY;if(b.forcescreen){var f=d,d={original:d.original?d.original:d};d.clientX=f.screenX;d.clientY=f.screenY}f=ofy=0;if(n&&!c){var l=b.win.position(),f=-l.left;ofy=-l.top}var h=d.clientY+ofy,i=b.rail.drag.st-(h-b.rail.drag.y);if(b.ishwscroll&&b.opt.bouncescroll)i<0?i=Math.round(i/2):i>b.page.maxh&&(i=b.page.maxh+
			Math.round((i-b.page.maxh)/2));else if(i<0&&(h=i=0),i>b.page.maxh)i=b.page.maxh,h=0;var s=d.clientX+f;if(b.railh&&b.railh.scrollable){var j=b.rail.drag.sl-(s-b.rail.drag.x);if(b.ishwscroll&&b.opt.bouncescroll)j<0?j=Math.round(j/2):j>b.page.maxw&&(j=b.page.maxw+Math.round((j-b.page.maxw)/2));else if(j<0&&(s=j=0),j>b.page.maxw)j=b.page.maxw,s=0}b.synched("touchmove",function(){b.rail.drag&&b.rail.drag.pt==2&&(b.prepareTransition&&b.prepareTransition(0),b.rail.scrollable&&b.setScrollTop(i),b.scrollmom.update(s,
			h),b.railh&&b.railh.scrollable?(b.setScrollLeft(j),b.showCursor(i,j)):b.showCursor(i),g.isie10&&document.selection.clear())});if(!g.ischrome&&!b.istouchcapable)return b.cancelEvent(d)}}}g.cantouch||b.opt.touchbehavior?(b.onpreventclick=function(d){if(b.preventclick)return b.preventclick.tg.onclick=b.preventclick.click,b.preventclick=false,b.cancelEvent(d)},b.onmousedown=b.ontouchstart,b.onmouseup=b.ontouchend,b.onclick=g.isios?false:function(d){return b.lastmouseup?(b.lastmouseup=false,b.cancelEvent(d)):
			true},b.onmousemove=b.ontouchmove,g.cursorgrabvalue&&(b.css(b.ispage?b.doc:b.win,{cursor:g.cursorgrabvalue}),b.css(b.rail,{cursor:g.cursorgrabvalue}))):(b.onmousedown=function(d,c){if(!(b.rail.drag&&b.rail.drag.pt!=1)){if(b.locked)return b.cancelEvent(d);b.cancelScroll();b.rail.drag={x:d.clientX,y:d.clientY,sx:b.scroll.x,sy:b.scroll.y,pt:1,hr:!!c};var f=b.getTarget(d);!b.ispage&&g.hasmousecapture&&f.setCapture();if(b.isiframe&&!g.hasmousecapture)b.saved.csspointerevents=b.doc.css("pointer-events"),
			b.css(b.doc,{"pointer-events":"none"});return b.cancelEvent(d)}},b.onmouseup=function(d){if(b.rail.drag&&(g.hasmousecapture&&document.releaseCapture(),b.isiframe&&!g.hasmousecapture&&b.doc.css("pointer-events",b.saved.csspointerevents),b.rail.drag.pt==1))return b.rail.drag=false,b.cancelEvent(d)},b.onmousemove=function(d){if(b.rail.drag){if(b.rail.drag.pt==1){if(g.ischrome&&d.which==0)return b.onmouseup(d);b.cursorfreezed=true;if(b.rail.drag.hr){b.scroll.x=b.rail.drag.sx+(d.clientX-b.rail.drag.x);
			if(b.scroll.x<0)b.scroll.x=0;var c=b.scrollvaluemaxw;if(b.scroll.x>c)b.scroll.x=c}else{b.scroll.y=b.rail.drag.sy+(d.clientY-b.rail.drag.y);if(b.scroll.y<0)b.scroll.y=0;c=b.scrollvaluemax;if(b.scroll.y>c)b.scroll.y=c}b.synched("mousemove",function(){b.rail.drag&&b.rail.drag.pt==1&&(b.showCursor(),b.rail.drag.hr?b.doScrollLeft(Math.round(b.scroll.x*b.scrollratio.x)):b.doScrollTop(Math.round(b.scroll.y*b.scrollratio.y)))});return b.cancelEvent(d)}}else b.checkarea=true});(g.cantouch||b.opt.touchbehavior)&&
		b.bind(b.win,"mousedown",b.onmousedown);g.hasmstouch&&(b.css(b.rail,{"-ms-touch-action":"none"}),b.css(b.cursor,{"-ms-touch-action":"none"}),b.bind(b.win,"MSPointerDown",b.ontouchstart),b.bind(document,"MSPointerUp",b.ontouchend),b.bind(document,"MSPointerMove",b.ontouchmove),b.bind(b.cursor,"MSGestureHold",function(b){b.preventDefault()}),b.bind(b.cursor,"contextmenu",function(b){b.preventDefault()}));this.istouchcapable&&(b.bind(b.win,"touchstart",b.ontouchstart),b.bind(document,"touchend",b.ontouchend),
			b.bind(document,"touchcancel",b.ontouchend),b.bind(document,"touchmove",b.ontouchmove));b.bind(b.cursor,"mousedown",b.onmousedown);b.bind(b.cursor,"mouseup",b.onmouseup);b.railh&&(b.bind(b.cursorh,"mousedown",function(d){b.onmousedown(d,true)}),b.bind(b.cursorh,"mouseup",function(d){if(!(b.rail.drag&&b.rail.drag.pt==2))return b.rail.drag=false,b.hasmoving=false,b.hideCursor(),g.hasmousecapture&&document.releaseCapture(),b.cancelEvent(d)}));b.bind(document,"mouseup",b.onmouseup);g.hasmousecapture&&
		b.bind(b.win,"mouseup",b.onmouseup);b.bind(document,"mousemove",b.onmousemove);b.onclick&&b.bind(document,"click",b.onclick);!g.cantouch&&!b.opt.touchbehavior&&(b.rail.mouseenter(function(){b.canshowonmouseevent&&b.showCursor();b.rail.active=true}),b.rail.mouseleave(function(){b.rail.active=false;b.rail.drag||b.hideCursor()}),b.opt.sensitiverail&&(b.rail.click(function(d){b.doRailClick(d,false,false)}),b.rail.dblclick(function(d){b.doRailClick(d,true,false)}),b.cursor.click(function(d){b.cancelEvent(d)}),
			b.cursor.dblclick(function(d){b.cancelEvent(d)})),b.railh&&(b.railh.mouseenter(function(){b.canshowonmouseevent&&b.showCursor();b.rail.active=true}),b.railh.mouseleave(function(){b.rail.active=false;b.rail.drag||b.hideCursor()})),b.zoom&&(b.zoom.mouseenter(function(){b.canshowonmouseevent&&b.showCursor();b.rail.active=true}),b.zoom.mouseleave(function(){b.rail.active=false;b.rail.drag||b.hideCursor()})));b.opt.enablemousewheel&&(b.isiframe||b.bind(g.isie&&b.ispage?document:b.docscroll,"mousewheel",
			b.onmousewheel),b.bind(b.rail,"mousewheel",b.onmousewheel),b.railh&&b.bind(b.railh,"mousewheel",b.onmousewheelhr));!b.ispage&&!g.cantouch&&!/HTML|BODY/.test(b.win[0].nodeName)&&(b.win.attr("tabindex")||b.win.attr({tabindex:B++}),b.win.focus(function(d){r=b.getTarget(d).id||true;b.hasfocus=true;b.canshowonmouseevent&&b.noticeCursor()}),b.win.blur(function(){r=false;b.hasfocus=false}),b.win.mouseenter(function(d){w=b.getTarget(d).id||true;b.hasmousefocus=true;b.canshowonmouseevent&&b.noticeCursor()}),
			b.win.mouseleave(function(){w=false;b.hasmousefocus=false}))}b.onkeypress=function(d){if(b.locked&&b.page.maxh==0)return true;var d=d?d:window.e,c=b.getTarget(d);if(c&&/INPUT|TEXTAREA|SELECT|OPTION/.test(c.nodeName)&&(!c.getAttribute("type")&&!c.type||!/submit|button|cancel/i.tp))return true;if(b.hasfocus||b.hasmousefocus&&!r||b.ispage&&!r&&!w){var c=d.keyCode,f=d.ctrlKey||false;if(b.locked&&c!=27)return b.cancelEvent(d);var l=false;switch(c){case 38:case 63233:b.doScrollBy(72);l=true;break;case 40:case 63235:b.doScrollBy(-72);
			l=true;break;case 37:case 63232:b.railh&&(f?b.doScrollLeft(0):b.doScrollLeftBy(72),l=true);break;case 39:case 63234:b.railh&&(f?b.doScrollLeft(b.page.maxw):b.doScrollLeftBy(-72),l=true);break;case 33:case 63276:b.doScrollBy(b.view.h);l=true;break;case 34:case 63277:b.doScrollBy(-b.view.h);l=true;break;case 36:case 63273:b.railh&&f?b.doScrollPos(0,0):b.doScrollTo(0);l=true;break;case 35:case 63275:b.railh&&f?b.doScrollPos(b.page.maxw,b.page.maxh):b.doScrollTo(b.page.maxh);l=true;break;case 32:b.opt.spacebarenabled&&
		(b.doScrollBy(-b.view.h),l=true);break;case 27:b.zoomactive&&(b.doZoom(),l=true)}if(l)return b.cancelEvent(d)}};b.opt.enablekeyboard&&b.bind(document,g.isopera&&!g.isopera12?"keypress":"keydown",b.onkeypress);b.bind(window,"resize",b.resize);b.bind(window,"orientationchange",b.resize);b.bind(window,"load",b.resize);if(g.ischrome&&!b.ispage&&!b.haswrapper){var m=b.win.attr("style"),f=parseFloat(b.win.css("width"))+1;b.win.css("width",f);b.synched("chromefix",function(){b.win.attr("style",m)})}b.onAttributeChange=
			function(){b.lazyResize()};if(!b.ispage&&!b.haswrapper)"WebKitMutationObserver"in window?(b.observer=new WebKitMutationObserver(function(d){d.forEach(b.onAttributeChange)}),b.observer.observe(b.win[0],{attributes:true,subtree:false})):(b.bind(b.win,g.isie&&!g.isie9?"propertychange":"DOMAttrModified",b.onAttributeChange),g.isie9&&b.win[0].attachEvent("onpropertychange",b.onAttributeChange));!b.ispage&&b.opt.boxzoom&&b.bind(window,"resize",b.resizeZoom);b.istextarea&&b.bind(b.win,"mouseup",b.resize);
		b.resize()}if(this.doc[0].nodeName=="IFRAME"){var A=function(){b.iframexd=false;try{var d="contentDocument"in this?this.contentDocument:this.contentWindow.document}catch(c){b.iframexd=true,d=false}if(b.iframexd)return"console"in window&&console.log("NiceScroll error: policy restriced iframe"),true;b.forcescreen=true;if(b.isiframe)b.iframe={doc:e(d),html:b.doc.contents().find("html")[0],body:b.doc.contents().find("body")[0]},b.getContentSize=function(){return{w:Math.max(b.iframe.html.scrollWidth,b.iframe.body.scrollWidth),
		h:Math.max(b.iframe.html.scrollHeight,b.iframe.body.scrollHeight)}},b.docscroll=e(b.iframe.body);if(!g.isios&&b.opt.iframeautoresize&&!b.isiframe){b.win.scrollTop(0);b.doc.height("");var f=Math.max(d.getElementsByTagName("html")[0].scrollHeight,d.body.scrollHeight);b.doc.height(f)}b.resize();g.isie7&&b.css(e(b.iframe.html),{"overflow-y":"hidden"});b.css(e(b.iframe.body),{"overflow-y":"hidden"});"contentWindow"in this?b.bind(this.contentWindow,"scroll",b.onscroll):b.bind(d,"scroll",b.onscroll);b.opt.enablemousewheel&&
	b.bind(d,"mousewheel",b.onmousewheel);b.opt.enablekeyboard&&b.bind(d,g.isopera?"keypress":"keydown",b.onkeypress);if(g.cantouch||b.opt.touchbehavior)b.bind(d,"mousedown",b.onmousedown),b.bind(d,"mousemove",function(d){b.onmousemove(d,true)}),g.cursorgrabvalue&&b.css(e(d.body),{cursor:g.cursorgrabvalue});b.bind(d,"mouseup",b.onmouseup);b.zoom&&(b.opt.dblclickzoom&&b.bind(d,"dblclick",b.doZoom),b.ongesturezoom&&b.bind(d,"gestureend",b.ongesturezoom))};this.doc[0].readyState&&this.doc[0].readyState==
		"complete"&&setTimeout(function(){A.call(b.doc[0],false)},500);b.bind(this.doc,"load",A)}};this.showCursor=function(d,c){if(b.cursortimeout)clearTimeout(b.cursortimeout),b.cursortimeout=0;if(b.rail){if(b.autohidedom)b.autohidedom.stop().css({opacity:b.opt.cursoropacitymax}),b.cursoractive=true;if(typeof d!="undefined"&&d!==false)b.scroll.y=Math.round(d*1/b.scrollratio.y);if(typeof c!="undefined")b.scroll.x=Math.round(c*1/b.scrollratio.x);b.cursor.css({height:b.cursorheight,top:b.scroll.y});if(b.cursorh)!b.rail.align&&
		b.rail.visibility?b.cursorh.css({width:b.cursorwidth,left:b.scroll.x+b.rail.width}):b.cursorh.css({width:b.cursorwidth,left:b.scroll.x}),b.cursoractive=true;b.zoom&&b.zoom.stop().css({opacity:b.opt.cursoropacitymax})}};this.hideCursor=function(d){if(!b.cursortimeout&&b.rail&&b.autohidedom)b.cursortimeout=setTimeout(function(){if(!b.rail.active||!b.showonmouseevent)b.autohidedom.stop().animate({opacity:b.opt.cursoropacitymin}),b.zoom&&b.zoom.stop().animate({opacity:b.opt.cursoropacitymin}),b.cursoractive=
		false;b.cursortimeout=0},d||400)};this.noticeCursor=function(d,c,f){b.showCursor(c,f);b.rail.active||b.hideCursor(d)};this.getContentSize=b.ispage?function(){return{w:Math.max(document.body.scrollWidth,document.documentElement.scrollWidth),h:Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}}:b.haswrapper?function(){return{w:b.doc.outerWidth()+parseInt(b.win.css("paddingLeft"))+parseInt(b.win.css("paddingRight")),h:b.doc.outerHeight()+parseInt(b.win.css("paddingTop"))+parseInt(b.win.css("paddingBottom"))}}:
		function(){return{w:b.docscroll[0].scrollWidth,h:b.docscroll[0].scrollHeight}};this.onResize=function(d,c){if(!b.win)return false;if(!b.haswrapper&&!b.ispage)if(b.win.css("display")=="none")return b.visibility&&b.hideRail().hideRailHr(),false;else!b.hidden&&!b.visibility&&b.showRail().showRailHr();var f=b.page.maxh,g=b.page.maxw,e=b.view.w;b.view={w:b.ispage?b.win.width():parseInt(b.win[0].clientWidth),h:b.ispage?b.win.height():parseInt(b.win[0].clientHeight)};b.page=c?c:b.getContentSize();b.page.maxh=
		Math.max(0,b.page.h-b.view.h);b.page.maxw=Math.max(0,b.page.w-b.view.w);if(b.page.maxh==f&&b.page.maxw==g&&b.view.w==e)if(b.ispage)return b;else{f=b.win.offset();if(b.lastposition&&(g=b.lastposition,g.top==f.top&&g.left==f.left))return b;b.lastposition=f}b.page.maxh==0?(b.hideRail(),b.scrollvaluemax=0,b.scroll.y=0,b.scrollratio.y=0,b.cursorheight=0,b.setScrollTop(0),b.rail.scrollable=false):b.rail.scrollable=true;b.page.maxw==0?(b.hideRailHr(),b.scrollvaluemaxw=0,b.scroll.x=0,b.scrollratio.x=0,b.cursorwidth=
		0,b.setScrollLeft(0),b.railh.scrollable=false):b.railh.scrollable=true;b.locked=b.page.maxh==0&&b.page.maxw==0;if(b.locked)return b.ispage||b.updateScrollBar(b.view),false;!b.hidden&&!b.visibility?b.showRail().showRailHr():!b.hidden&&!b.railh.visibility&&b.showRailHr();b.istextarea&&b.win.css("resize")&&b.win.css("resize")!="none"&&(b.view.h-=20);b.ispage||b.updateScrollBar(b.view);b.cursorheight=Math.min(b.view.h,Math.round(b.view.h*(b.view.h/b.page.h)));b.cursorheight=Math.max(b.opt.cursorminheight,
		b.cursorheight);b.cursorwidth=Math.min(b.view.w,Math.round(b.view.w*(b.view.w/b.page.w)));b.cursorwidth=Math.max(b.opt.cursorminheight,b.cursorwidth);b.scrollvaluemax=b.view.h-b.cursorheight-b.cursor.hborder;if(b.railh)b.railh.width=b.page.maxh>0?b.view.w-b.rail.width:b.view.w,b.scrollvaluemaxw=b.railh.width-b.cursorwidth-b.cursorh.wborder;b.scrollratio={x:b.page.maxw/b.scrollvaluemaxw,y:b.page.maxh/b.scrollvaluemax};b.getScrollTop()>b.page.maxh?b.doScroll(b.page.maxh):(b.scroll.y=Math.round(b.getScrollTop()*
		(1/b.scrollratio.y)),b.scroll.x=Math.round(b.getScrollLeft()*(1/b.scrollratio.x)),b.cursoractive&&b.noticeCursor());b.scroll.y&&b.getScrollTop()==0&&b.doScrollTo(Math.floor(b.scroll.y*b.scrollratio.y));return b};this.resize=function(){b.delayed("resize",b.onResize,30);return b};this.lazyResize=function(){b.delayed("resize",b.resize,250)};this._bind=function(d,c,f,g){b.events.push({e:d,n:c,f:f,b:g});d.addEventListener?d.addEventListener(c,f,g||false):d.attachEvent?d.attachEvent("on"+c,f):d["on"+c]=
		f};this.bind=function(d,c,f,e){var h="jquery"in d?d[0]:d;h.addEventListener?(g.cantouch&&/mouseup|mousedown|mousemove/.test(c)&&b._bind(h,c=="mousedown"?"touchstart":c=="mouseup"?"touchend":"touchmove",function(b){if(b.touches){if(b.touches.length<2){var d=b.touches.length?b.touches[0]:b;d.original=b;f.call(this,d)}}else if(b.changedTouches)d=b.changedTouches[0],d.original=b,f.call(this,d)},e||false),b._bind(h,c,f,e||false),c=="mousewheel"&&b._bind(h,"DOMMouseScroll",f,e||false),g.cantouch&&c=="mouseup"&&
		b._bind(h,"touchcancel",f,e||false)):b._bind(h,c,function(d){if((d=d||window.event||false)&&d.srcElement)d.target=d.srcElement;return f.call(h,d)===false||e===false?b.cancelEvent(d):true})};this._unbind=function(b,c,f,g){b.removeEventListener?b.removeEventListener(c,f,g):b.detachEvent?b.detachEvent("on"+c,f):b["on"+c]=false};this.unbindAll=function(){for(var d=0;d<b.events.length;d++){var c=b.events[d];b._unbind(c.e,c.n,c.f,c.b)}};this.cancelEvent=function(b){b=b.original?b.original:b?b:window.event||
		false;if(!b)return false;b.preventDefault&&b.preventDefault();b.stopPropagation&&b.stopPropagation();b.preventManipulation&&b.preventManipulation();b.cancelBubble=true;b.cancel=true;return b.returnValue=false};this.stopPropagation=function(b){b=b.original?b.original:b?b:window.event||false;if(!b)return false;if(b.stopPropagation)return b.stopPropagation();if(b.cancelBubble)b.cancelBubble=true;return false};this.showRail=function(){if(b.page.maxh!=0&&(b.ispage||b.win.css("display")!="none"))b.visibility=
		true,b.rail.visibility=true,b.rail.css("display","block");return b};this.showRailHr=function(){if(!b.railh)return b;if(b.page.maxw!=0&&(b.ispage||b.win.css("display")!="none"))b.railh.visibility=true,b.railh.css("display","block");return b};this.hideRail=function(){b.visibility=false;b.rail.visibility=false;b.rail.css("display","none");return b};this.hideRailHr=function(){if(!b.railh)return b;b.railh.visibility=false;b.railh.css("display","none");return b};this.show=function(){b.hidden=false;b.locked=
		false;return b.showRail().showRailHr()};this.hide=function(){b.hidden=true;b.locked=true;return b.hideRail().hideRailHr()};this.toggle=function(){return b.hidden?b.show():b.hide()};this.remove=function(){b.doZoomOut();b.unbindAll();b.observer!==false&&b.observer.disconnect();b.events=[];if(b.cursor)b.cursor.remove(),b.cursor=null;if(b.cursorh)b.cursorh.remove(),b.cursorh=null;if(b.rail)b.rail.remove(),b.rail=null;if(b.railh)b.railh.remove(),b.railh=null;if(b.zoom)b.zoom.remove(),b.zoom=null;for(var d=
		0;d<b.saved.css.length;d++){var c=b.saved.css[d];c[0].css(c[1],typeof c[2]=="undefined"?"":c[2])}b.saved=false;b.me.data("__nicescroll","");b.me=null;b.doc=null;b.docscroll=null;b.win=null;return b};this.scrollstart=function(d){this.onscrollstart=d;return b};this.scrollend=function(d){this.onscrollend=d;return b};this.scrollcancel=function(d){this.onscrollcancel=d;return b};this.zoomin=function(d){this.onzoomin=d;return b};this.zoomout=function(d){this.onzoomout=d;return b};this.isScrollable=function(b){for(b=
			                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 b.target?b.target:b;b&&b.nodeType==1&&!/BODY|HTML/.test(b.nodeName);){var c=e(b);if(/scroll|auto/.test(c.css("overflowY")||c.css("overflowX")||c.css("overflow")||""))return b.clientHeight!=b.scrollHeight;b=b.parentNode?b.parentNode:false}return false};this.getViewport=function(b){for(b=b&&b.parentNode?b.parentNode:false;b&&b.nodeType==1&&!/BODY|HTML/.test(b.nodeName);){var c=e(b);if(/scroll|auto/.test(c.css("overflowY")||c.css("overflowX")||c.css("overflow")||"")&&b.clientHeight!=b.scrollHeight)return c;
		if(c.getNiceScroll().length>0)return c;b=b.parentNode?b.parentNode:false}return false};this.onmousewheel=function(d){if(b.locked)return true;if(!b.rail.scrollable)return b.railh&&b.railh.scrollable?b.onmousewheelhr(d):true;if(b.opt.preservenativescrolling&&b.checkarea)b.checkarea=false,b.nativescrollingarea=b.isScrollable(d);if(b.nativescrollingarea)return true;if(b.locked)return b.cancelEvent(d);if(b.rail.drag)return b.cancelEvent(d);i(d,false);return b.cancelEvent(d)};this.onmousewheelhr=function(d){if(b.locked||
		!b.railh.scrollable)return true;if(b.opt.preservenativescrolling&&b.checkarea)b.checkarea=false,b.nativescrollingarea=b.isScrollable(d);if(b.nativescrollingarea)return true;if(b.locked)return b.cancelEvent(d);if(b.rail.drag)return b.cancelEvent(d);i(d,true);return b.cancelEvent(d)};this.stop=function(){b.cancelScroll();b.scrollmon&&b.scrollmon.stop();b.cursorfreezed=false;b.scroll.y=Math.round(b.getScrollTop()*(1/b.scrollratio.y));b.noticeCursor();return b};this.getTransitionSpeed=function(d){var c=
		Math.round(b.opt.scrollspeed*10),d=Math.min(c,Math.round(d/20*b.opt.scrollspeed));return d>20?d:0};b.opt.smoothscroll?b.ishwscroll&&g.hastransition&&b.opt.usetransition?(this.prepareTransition=function(c,e){var f=e?c>20?c:0:b.getTransitionSpeed(c),h=f?g.prefixstyle+"transform "+f+"ms ease-out":"";if(!b.lasttransitionstyle||b.lasttransitionstyle!=h)b.lasttransitionstyle=h,b.doc.css(g.transitionstyle,h);return f},this.doScrollLeft=function(c,g){var f=b.scrollrunning?b.newscrolly:b.getScrollTop();b.doScrollPos(c,
		f,g)},this.doScrollTop=function(c,g){var f=b.scrollrunning?b.newscrollx:b.getScrollLeft();b.doScrollPos(f,c,g)},this.doScrollPos=function(c,e,f){var h=b.getScrollTop(),i=b.getScrollLeft();((b.newscrolly-h)*(e-h)<0||(b.newscrollx-i)*(c-i)<0)&&b.cancelScroll();if(b.opt.bouncescroll==false){if(e<0)e=0;else if(e>b.page.maxh)e=b.page.maxh;if(c<0)c=0;else if(c>b.page.maxw)c=b.page.maxw}if(c==b.newscrollx&&e==b.newscrolly)return false;b.newscrolly=e;b.newscrollx=c;b.newscrollspeed=f||false;if(b.timer)return false;
		b.timer=setTimeout(function(){var f=b.getScrollTop(),h=b.getScrollLeft(),i,j;i=c-h;j=e-f;i=Math.round(Math.sqrt(Math.pow(i,2)+Math.pow(j,2)));i=b.prepareTransition(b.newscrollspeed?b.newscrollspeed:i);b.timerscroll&&b.timerscroll.tm&&clearInterval(b.timerscroll.tm);if(i>0){!b.scrollrunning&&b.onscrollstart&&b.onscrollstart.call(b,{type:"scrollstart",current:{x:h,y:f},request:{x:c,y:e},end:{x:b.newscrollx,y:b.newscrolly},speed:i});if(g.transitionend){if(!b.scrollendtrapped)b.scrollendtrapped=true,
			b.bind(b.doc,g.transitionend,b.onScrollEnd,false)}else b.scrollendtrapped&&clearTimeout(b.scrollendtrapped),b.scrollendtrapped=setTimeout(b.onScrollEnd,i);b.timerscroll={bz:new BezierClass(f,b.newscrolly,i,0,0,0.58,1),bh:new BezierClass(h,b.newscrollx,i,0,0,0.58,1)};if(!b.cursorfreezed)b.timerscroll.tm=setInterval(function(){b.showCursor(b.getScrollTop(),b.getScrollLeft())},60)}b.synched("doScroll-set",function(){b.timer=0;if(b.scrollendtrapped)b.scrollrunning=true;b.setScrollTop(b.newscrolly);b.setScrollLeft(b.newscrollx);
			if(!b.scrollendtrapped)b.onScrollEnd()})},50)},this.cancelScroll=function(){if(!b.scrollendtrapped)return true;var c=b.getScrollTop(),e=b.getScrollLeft();b.scrollrunning=false;g.transitionend||clearTimeout(g.transitionend);b.scrollendtrapped=false;b._unbind(b.doc,g.transitionend,b.onScrollEnd);b.prepareTransition(0);b.setScrollTop(c);b.railh&&b.setScrollLeft(e);b.timerscroll&&b.timerscroll.tm&&clearInterval(b.timerscroll.tm);b.timerscroll=false;b.cursorfreezed=false;b.showCursor(c,e);return b},this.onScrollEnd=
		function(){b.scrollendtrapped&&b._unbind(b.doc,g.transitionend,b.onScrollEnd);b.scrollendtrapped=false;b.prepareTransition(0);b.timerscroll&&b.timerscroll.tm&&clearInterval(b.timerscroll.tm);b.timerscroll=false;var c=b.getScrollTop(),e=b.getScrollLeft();b.setScrollTop(c);b.railh&&b.setScrollLeft(e);b.noticeCursor(false,c,e);b.cursorfreezed=false;if(c<0)c=0;else if(c>b.page.maxh)c=b.page.maxh;if(e<0)e=0;else if(e>b.page.maxw)e=b.page.maxw;if(c!=b.newscrolly||e!=b.newscrollx)return b.doScrollPos(e,
			c,b.opt.snapbackspeed);b.onscrollend&&b.scrollrunning&&b.onscrollend.call(b,{type:"scrollend",current:{x:e,y:c},end:{x:b.newscrollx,y:b.newscrolly}});b.scrollrunning=false}):(this.doScrollLeft=function(c){var e=b.scrollrunning?b.newscrolly:b.getScrollTop();b.doScrollPos(c,e)},this.doScrollTop=function(c){var e=b.scrollrunning?b.newscrollx:b.getScrollLeft();b.doScrollPos(e,c)},this.doScrollPos=function(c,e){function f(){if(b.cancelAnimationFrame)return true;b.scrollrunning=true;if(n=1-n)return b.timer=
		p(f)||1;var c=0,d=sy=b.getScrollTop();if(b.dst.ay){var d=b.bzscroll?b.dst.py+b.bzscroll.getNow()*b.dst.ay:b.newscrolly,e=d-sy;if(e<0&&d<b.newscrolly||e>0&&d>b.newscrolly)d=b.newscrolly;b.setScrollTop(d);d==b.newscrolly&&(c=1)}else c=1;var g=sx=b.getScrollLeft();if(b.dst.ax){g=b.bzscroll?b.dst.px+b.bzscroll.getNow()*b.dst.ax:b.newscrollx;e=g-sx;if(e<0&&g<b.newscrollx||e>0&&g>b.newscrollx)g=b.newscrollx;b.setScrollLeft(g);g==b.newscrollx&&(c+=1)}else c+=1;if(c==2){b.timer=0;b.cursorfreezed=false;b.bzscroll=
		false;b.scrollrunning=false;if(d<0)d=0;else if(d>b.page.maxh)d=b.page.maxh;if(g<0)g=0;else if(g>b.page.maxw)g=b.page.maxw;g!=b.newscrollx||d!=b.newscrolly?b.doScrollPos(g,d):b.onscrollend&&b.onscrollend.call(b,{type:"scrollend",current:{x:sx,y:sy},end:{x:b.newscrollx,y:b.newscrolly}})}else b.timer=p(f)||1}e=typeof e=="undefined"||e===false?b.getScrollTop(true):e;if(b.timer&&b.newscrolly==e&&b.newscrollx==c)return true;b.timer&&q(b.timer);b.timer=0;var g=b.getScrollTop(),h=b.getScrollLeft();((b.newscrolly-
		g)*(e-g)<0||(b.newscrollx-h)*(c-h)<0)&&b.cancelScroll();b.newscrolly=e;b.newscrollx=c;if(!b.bouncescroll||!b.rail.visibility)if(b.newscrolly<0)b.newscrolly=0;else if(b.newscrolly>b.page.maxh)b.newscrolly=b.page.maxh;if(!b.bouncescroll||!b.railh.visibility)if(b.newscrollx<0)b.newscrollx=0;else if(b.newscrollx>b.page.maxw)b.newscrollx=b.page.maxw;b.dst={};b.dst.x=c-h;b.dst.y=e-g;b.dst.px=h;b.dst.py=g;var i=Math.round(Math.sqrt(Math.pow(b.dst.x,2)+Math.pow(b.dst.y,2)));b.dst.ax=b.dst.x/i;b.dst.ay=b.dst.y/
		i;var j=0,k=i;if(b.dst.x==0)j=g,k=e,b.dst.ay=1,b.dst.py=0;else if(b.dst.y==0)j=h,k=c,b.dst.ax=1,b.dst.px=0;i=b.getTransitionSpeed(i);b.bzscroll=i>0?b.bzscroll?b.bzscroll.update(k,i):new BezierClass(j,k,i,0,1,0,1):false;if(!b.timer){(g==b.page.maxh&&e>=b.page.maxh||h==b.page.maxw&&c>=b.page.maxw)&&b.checkContentSize();var n=1;b.cancelAnimationFrame=false;b.timer=1;b.onscrollstart&&!b.scrollrunning&&b.onscrollstart.call(b,{type:"scrollstart",current:{x:h,y:g},request:{x:c,y:e},end:{x:b.newscrollx,y:b.newscrolly},
		speed:i});f();(g==b.page.maxh&&e>=g||h==b.page.maxw&&c>=h)&&b.checkContentSize();b.noticeCursor()}},this.cancelScroll=function(){b.timer&&q(b.timer);b.timer=0;b.bzscroll=false;b.scrollrunning=false;return b}):(this.doScrollLeft=function(c,e){var f=b.getScrollTop();b.doScrollPos(c,f,e)},this.doScrollTop=function(c,e){var f=b.getScrollLeft();b.doScrollPos(f,c,e)},this.doScrollPos=function(c,e){var f=c>b.page.maxw?b.page.maxw:c;f<0&&(f=0);var g=e>b.page.maxh?b.page.maxh:e;g<0&&(g=0);b.synched("scroll",
		function(){b.setScrollTop(g);b.setScrollLeft(f)})},this.cancelScroll=function(){});this.doScrollBy=function(c,e){var f=0,f=e?Math.floor((b.scroll.y-c)*b.scrollratio.y):(b.timer?b.newscrolly:b.getScrollTop(true))-c;if(b.bouncescroll){var g=Math.round(b.view.h/2);f<-g?f=-g:f>b.page.maxh+g&&(f=b.page.maxh+g)}b.cursorfreezed=false;py=b.getScrollTop(true);if(f<0&&py<=0)return b.noticeCursor();else if(f>b.page.maxh&&py>=b.page.maxh)return b.checkContentSize(),b.noticeCursor();b.doScrollTop(f)};this.doScrollLeftBy=
		function(c,e){var f=0,f=e?Math.floor((b.scroll.x-c)*b.scrollratio.x):(b.timer?b.newscrollx:b.getScrollLeft(true))-c;if(b.bouncescroll){var g=Math.round(b.view.w/2);f<-g?f=-g:f>b.page.maxw+g&&(f=b.page.maxw+g)}b.cursorfreezed=false;px=b.getScrollLeft(true);if(f<0&&px<=0)return b.noticeCursor();else if(f>b.page.maxw&&px>=b.page.maxw)return b.noticeCursor();b.doScrollLeft(f)};this.doScrollTo=function(c,e){e&&Math.round(c*b.scrollratio.y);b.cursorfreezed=false;b.doScrollTop(c)};this.checkContentSize=
		function(){var c=b.getContentSize();(c.h!=b.page.h||c.w!=b.page.w)&&b.resize(false,c)};b.onscroll=function(){b.rail.drag||b.cursorfreezed||b.synched("scroll",function(){b.scroll.y=Math.round(b.getScrollTop()*(1/b.scrollratio.y));if(b.railh)b.scroll.x=Math.round(b.getScrollLeft()*(1/b.scrollratio.x));b.noticeCursor()})};b.bind(b.docscroll,"scroll",b.onscroll);this.doZoomIn=function(c){if(!b.zoomactive){b.zoomactive=true;b.zoomrestore={style:{}};var h="position,top,left,zIndex,backgroundColor,marginTop,marginBottom,marginLeft,marginRight".split(","),
		f=b.win[0].style,i;for(i in h){var j=h[i];b.zoomrestore.style[j]=typeof f[j]!="undefined"?f[j]:""}b.zoomrestore.style.width=b.win.css("width");b.zoomrestore.style.height=b.win.css("height");b.zoomrestore.padding={w:b.win.outerWidth()-b.win.width(),h:b.win.outerHeight()-b.win.height()};if(g.isios4)b.zoomrestore.scrollTop=e(window).scrollTop(),e(window).scrollTop(0);b.win.css({position:g.isios4?"absolute":"fixed",top:0,left:0,"z-index":b.opt.zindex+100,margin:"0px"});h=b.win.css("backgroundColor");
		(h==""||/transparent|rgba\(0, 0, 0, 0\)|rgba\(0,0,0,0\)/.test(h))&&b.win.css("backgroundColor","#fff");b.rail.css({"z-index":b.opt.zindex+110});b.zoom.css({"z-index":b.opt.zindex+112});b.zoom.css("backgroundPosition","0px -18px");b.resizeZoom();b.onzoomin&&b.onzoomin.call(b);return b.cancelEvent(c)}};this.doZoomOut=function(c){if(b.zoomactive)return b.zoomactive=false,b.win.css("margin",""),b.win.css(b.zoomrestore.style),g.isios4&&e(window).scrollTop(b.zoomrestore.scrollTop),b.rail.css({"z-index":b.ispage?
		b.opt.zindex:b.opt.zindex+2}),b.zoom.css({"z-index":b.opt.zindex}),b.zoomrestore=false,b.zoom.css("backgroundPosition","0px 0px"),b.onResize(),b.onzoomout&&b.onzoomout.call(b),b.cancelEvent(c)};this.doZoom=function(c){return b.zoomactive?b.doZoomOut(c):b.doZoomIn(c)};this.resizeZoom=function(){if(b.zoomactive){var c=b.getScrollTop();b.win.css({width:e(window).width()-b.zoomrestore.padding.w+"px",height:e(window).height()-b.zoomrestore.padding.h+"px"});b.onResize();b.setScrollTop(Math.min(b.page.maxh,
		c))}};this.init();e.nicescroll.push(this)},z=function(e){var c=this;this.nc=e;this.steptime=this.lasttime=this.speedy=this.speedx=this.lasty=this.lastx=0;this.snapy=this.snapx=false;this.demuly=this.demulx=0;this.lastscrolly=this.lastscrollx=-1;this.timer=this.chky=this.chkx=0;this.time=function(){return+new Date};this.reset=function(e,i){c.stop();var b=c.time();c.steptime=0;c.lasttime=b;c.speedx=0;c.speedy=0;c.lastx=e;c.lasty=i;c.lastscrollx=-1;c.lastscrolly=-1};this.update=function(e,i){var b=c.time();
	c.steptime=b-c.lasttime;c.lasttime=b;var b=i-c.lasty,j=e-c.lastx,g=c.nc.getScrollTop(),m=c.nc.getScrollLeft();g+=b;m+=j;c.snapx=m<0||m>c.nc.page.maxw;c.snapy=g<0||g>c.nc.page.maxh;c.speedx=j;c.speedy=b;c.lastx=e;c.lasty=i};this.stop=function(){c.nc.unsynched("domomentum2d");c.timer&&clearTimeout(c.timer);c.timer=0;c.lastscrollx=-1;c.lastscrolly=-1};this.doSnapy=function(e,i){var b=false;if(i<0)i=0,b=true;else if(i>c.nc.page.maxh)i=c.nc.page.maxh,b=true;if(e<0)e=0,b=true;else if(e>c.nc.page.maxw)e=
	c.nc.page.maxw,b=true;b&&c.nc.doScrollPos(e,i,c.nc.opt.snapbackspeed)};this.doMomentum=function(e){var i=c.time(),b=e?i+e:c.lasttime,e=c.nc.getScrollLeft(),j=c.nc.getScrollTop(),g=c.nc.page.maxh,m=c.nc.page.maxw;c.speedx=m>0?Math.min(60,c.speedx):0;c.speedy=g>0?Math.min(60,c.speedy):0;b=b&&i-b<=50;if(j<0||j>g||e<0||e>m)b=false;e=c.speedx&&b?c.speedx:false;if(c.speedy&&b&&c.speedy||e){var o=Math.max(16,c.steptime);o>50&&(e=o/50,c.speedx*=e,c.speedy*=e,o=50);c.demulxy=0;c.lastscrollx=c.nc.getScrollLeft();
	c.chkx=c.lastscrollx;c.lastscrolly=c.nc.getScrollTop();c.chky=c.lastscrolly;var d=c.lastscrollx,l=c.lastscrolly,f=function(){var b=c.time()-i>600?0.04:0.02;if(c.speedx&&(d=Math.floor(c.lastscrollx-c.speedx*(1-c.demulxy)),c.lastscrollx=d,d<0||d>m))b=0.1;if(c.speedy&&(l=Math.floor(c.lastscrolly-c.speedy*(1-c.demulxy)),c.lastscrolly=l,l<0||l>g))b=0.1;c.demulxy=Math.min(1,c.demulxy+b);c.nc.synched("domomentum2d",function(){if(c.speedx)c.nc.getScrollLeft()!=c.chkx&&c.stop(),c.chkx=d,c.nc.setScrollLeft(d);
		if(c.speedy)c.nc.getScrollTop()!=c.chky&&c.stop(),c.chky=l,c.nc.setScrollTop(l);c.timer||(c.nc.hideCursor(),c.doSnapy(d,l))});c.demulxy<1?c.timer=setTimeout(f,o):(c.stop(),c.nc.hideCursor(),c.doSnapy(d,l))};f()}else c.doSnapy(c.nc.getScrollLeft(),c.nc.getScrollTop())}},t=e.fn.scrollTop;e.cssHooks.pageYOffset={get:function(j){var c=e.data(j,"__nicescroll")||false;return c&&c.ishwscroll?c.getScrollTop():t.call(j)},set:function(j,c){var h=e.data(j,"__nicescroll")||false;h&&h.ishwscroll?h.setScrollTop(parseInt(c)):
	t.call(j,c);return this}};e.fn.scrollTop=function(j){if(typeof j=="undefined"){var c=this[0]?e.data(this[0],"__nicescroll")||false:false;return c&&c.ishwscroll?c.getScrollTop():t.call(this)}else return this.each(function(){var c=e.data(this,"__nicescroll")||false;c&&c.ishwscroll?c.setScrollTop(parseInt(j)):t.call(e(this),j)})};var u=e.fn.scrollLeft;e.cssHooks.pageXOffset={get:function(j){var c=e.data(j,"__nicescroll")||false;return c&&c.ishwscroll?c.getScrollLeft():u.call(j)},set:function(j,c){var h=
	e.data(j,"__nicescroll")||false;h&&h.ishwscroll?h.setScrollLeft(parseInt(c)):u.call(j,c);return this}};e.fn.scrollLeft=function(j){if(typeof j=="undefined"){var c=this[0]?e.data(this[0],"__nicescroll")||false:false;return c&&c.ishwscroll?c.getScrollLeft():u.call(this)}else return this.each(function(){var c=e.data(this,"__nicescroll")||false;c&&c.ishwscroll?c.setScrollLeft(parseInt(j)):u.call(e(this),j)})};var v=function(j){var c=this;this.length=0;this.name="nicescrollarray";this.each=function(e){for(var b=
	0;b<c.length;b++)e.call(c[b]);return c};this.push=function(e){c[c.length]=e;c.length++};this.eq=function(e){return c[e]};if(j)for(a=0;a<j.length;a++){var h=e.data(j[a],"__nicescroll")||false;h&&(this[this.length]=h,this.length++)}return this};(function(e,c,h){for(var i=0;i<c.length;i++)h(e,c[i])})(v.prototype,"show,hide,toggle,onResize,resize,remove,stop,doScrollPos".split(","),function(e,c){e[c]=function(){var e=arguments;return this.each(function(){this[c].apply(this,e)})}});e.fn.getNiceScroll=
	function(j){return typeof j=="undefined"?new v(this):e.data(this[j],"__nicescroll")||false};e.extend(e.expr[":"],{nicescroll:function(j){return e.data(j,"__nicescroll")?true:false}});e.fn.niceScroll=function(j,c){typeof c=="undefined"&&typeof j=="object"&&!("jquery"in j)&&(c=j,j=false);var h=new v;typeof c=="undefined"&&(c={});if(j)c.doc=e(j),c.win=e(this);var i=!("doc"in c);if(!i&&!("win"in c))c.win=e(this);this.each(function(){var b=e(this).data("__nicescroll")||false;if(!b)c.doc=i?e(this):c.doc,
	b=new F(c,e(this)),e(this).data("__nicescroll",b);h.push(b)});return h.length==1?h[0]:h};window.NiceScroll={getjQuery:function(){return e}};if(!e.nicescroll)e.nicescroll=new v})(jQuery);