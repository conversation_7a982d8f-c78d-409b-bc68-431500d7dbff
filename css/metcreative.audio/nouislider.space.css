	.noUiSlider,
	.noUiSlider * {
-webkit-box-sizing: border-box;
   -moz-box-sizing: border-box;
		box-sizing: border-box;
-webkit-user-select: none;
   -moz-user-select: none;
	-ms-user-select: none;
		display: block;
		cursor: default;
	}
	.noUiSlider {
		position: absolute;
		-webkit-transform: translateY(-100%);
		-moz-transform: translateY(-100%);
		-ms-transform: translateY(-100%);
		-o-transform: translateY(-100%);
	}
	.noUiSlider a {
		position: absolute;
		z-index: 1;
	}
	.noUiSlider a:nth-child(2) {
		background: inherit !important;
	}
	.noUiSlider.vertical a {
		width: 100%;
		bottom: 0;
	}
	.noUiSlider.horizontal a {
		height: 100%;
		right: 0;
	}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	.noUiSlider:before,
	body.TOUCH,
	.noUiSlider div {
		-ms-touch-action: none;
	}
	.noUiSlider:before {
		display: block;
		position: absolute;
		width: 150%;
		left: -25%;
		height: 400%;
		top: -150%;
		content: "";
		z-index: -1;
	}
	.noUiSlider.vertical:before {
		width: 400%;
		left: -150%;
		height: 150%;
		top: -25%;
	}
}
	.noUiSlider {
		background: #fff;
		border: 0 solid #fff;
	}
	.noUiSlider.disabled div:before,
	.noUiSlider.disabled {
		background: #ccc;
		border-color: #ccc;
	}
	.noUiSlider.horizontal {
		border-width: 0;
	}
	.noUiSlider.vertical {
		border-width: 12px 0;
	}
	.noUiSlider div {
		height: 20px;
		width: 5px;
		position: relative;
	}
	.noUiSlider div:before {
		position: absolute;
		background: transparent;
		content: "";
	}
	.noUiSlider.horizontal div:before {
	}
	.noUiSlider.vertical div:before {
		top: 10px;
		width: 4px;
		height: 24px;
	}
	.noUiSlider:not(.disabled) div.active:after,
	.noUiSlider:not(.disabled) div:hover:after {

	}
	.noUiSlider.horizontal {
		width: 100px;
		height: 5px;
		top: 55%;
	}
	.noUiSlider.horizontal div {
		margin: 0 0 0 -5px;
		margin-top: -7.5px;
	}
	.noUiSlider.vertical {
		width: 1px;
		height: 100px;
	}
	.noUiSlider.vertical div {
		margin: -22px 0 0 1px;
	}