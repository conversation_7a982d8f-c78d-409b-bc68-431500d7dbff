/*****************************************************************
 * **************************************************************
 * @MetCreative - Table of Contents
 * 1-) GLOBAL
 *    a- General
 *    b- Header
 *    c- Main Menu
 *    d- Slider
 *    e- Image With Text Boxes
 *    f- Tab Content with Icons
 *    g- Pagers Near Title
 *    h- Latest Posts Widget
 *    i- Recent Works Widget
 *    j- Twitter Widget
 *    k- Blog Posts List
 *    l- Twitter Ticker
 *    m- Footer
 *    n- Page Header
 *    o- Team Members
 *    p- Cacoon Sidebar
 *    q- Service Box
 *    r- 404
 *    s- Contact Page
 *    t- Blog
 *    u- Flickr Widget
 *    v- Comments
 *    w- Pagination
 *    x- Portfolio List
 *    y- Skill Circles
 *    z- Background Images and Patterns
 *    aa- Cacoon Slider
 *    ab- Buttons
 *    ac- Custom Accordion
 *    ad- Testimonials
 *    ae- Message Boxes
 *    af- Progress Bars
 *    ag- Pagination
 *    ah- Tab Content
 *    ai- Gallery
 *    aj- External Contact Form
 * 2-) Response For Lower Than 1170
 * 3-) Response For Tablets
 * 4-) Response For Mobile
 * !Note: You can make search with one of the title above to find the block according to it
 * **************************************************************
 *****************************************************************/

/*-------------------------------
	General
 ------------------------------*/
.met_color {
	color : #18ADB5;
}

.met_color2 {
	color : #FFFFFF;
}

.met_color3 {
	color : #656870;
}

.met_bgcolor {
	background-color : #18ADB5;
}

.met_bgcolor2 {
	background-color : #5B5E65;
}

.met_bgcolor3 {
	background-color : #656870;
}

.met_bgcolor4 {
	background-color : #7E8A96;
}

.met_bgcolor5 {
	background-color : #A4AEB9;
}

.met_bgcolor6 {
	background-color : #656870;
}

.met_bgcolor7 {
	background-color : #46484E;
}

.met_bgcolor_trans {
	background-color : rgba(24, 173, 181, 0.8);
}

.met_bgcolor5_trans {
	background-color : rgba(164, 174, 185, 0.5);
}

.met_bgcolor6_trans {
	background-color : rgba(101, 104, 112, 0.5);
}

.met_color_transition {
	-webkit-transition : color ease-out 0.4s;
	-moz-transition    : color ease-out 0.4s;
	-ms-transition     : color ease-out 0.4s;
	-o-transition      : color ease-out 0.4s;
}

.met_color_transition:hover {
	color           : #18ADB5;
	text-decoration : none;
}

.met_bgcolor_transition,
.met_bgcolor_transition2 {
	-webkit-transition : background-color ease-out 0.4s;
	-moz-transition    : background-color ease-out 0.4s;
	-ms-transition     : background-color ease-out 0.4s;
	-o-transition      : background-color ease-out 0.4s;
}

.met_bgcolor_transition:hover {
	background-color : #18ADB5;
}

.met_bgcolor_transition2:hover {
	background-color : #46484E;
}

:focus {
	outline : none;
}

:active {
	outline : none;
}

:-o-any-link:focus {
	outline : none;
}

::-moz-focus-inner {
	border : 0;
}

::-o-focus-inner {
	border : 0;
}

.met_bgcolor_transition:hover a,
.met_bgcolor_transition2:hover a {
	text-decoration : none;
}

.met_page_wrapper {
	background-color : #FFFFFF;
	overflow         : hidden;
	position         : relative;
	z-index          : 1050;
}

.met_page_wrapper.met_boxed_layout {
	margin        : 0 auto;
	width         : 1210px;
	box-shadow    : 0 0 5px #CCCCCC;
	padding-right : 0;
	position      : relative;
	overflow      : visible;
}

.met_boxed_layout .met_content {
	width        : auto;
	margin-left  : 20px;
	margin-right : 20px;
}

.met_content {
	width    : 1170px;
	margin   : 0 auto;
	position : relative;
	z-index  : 3;
}

.row-fluid {
	margin-bottom : 30px
}

.row-fluid .row-fluid:last-child {
	margin-bottom : 0
}

h1.met_bold_one,
h2.met_bold_one,
h3.met_bold_one,
h4.met_bold_one,
h5.met_bold_one,
h6.met_bold_one {
	font-weight : 700
}
h1.met_bold_two,
h2.met_bold_two,
h3.met_bold_two,
h4.met_bold_two,
h5.met_bold_two,
h6.met_bold_two {
	font-weight : 700;
}
h4.met_bold_two{
	font-size: 16px;
}

.met_button {
	color              : #FFFFFF;
	padding            : 0 40px 0 19px;
	font-size          : 12px;
	line-height        : 40px;
	height             : 40px;
	font-weight        : 700;
	position           : relative;
	display            : inline-block;
	-webkit-transition : background-color ease-out 0.4s;
	-moz-transition    : background-color ease-out 0.4s;
	-ms-transition     : background-color ease-out 0.4s;
	-o-transition      : background-color ease-out 0.4s;
}

.met_button:hover {
	text-decoration  : none;
	background-color : #5B5E65;
}

.met_button:after {
	font-family : FontAwesome;
	content     : '\f067';
	font-size   : 14px;
	line-height : 40px;
	color       : #FFFFFF;
	position    : absolute;
	right       : 0;
	top         : 0;
	height      : 100%;
	width       : 40px;
	text-align  : center;
	display     : block;
}

.met_clear_margin_top {
	margin-top    : 0;
	margin-bottom : 25px;
}

.met_text_block {
	background-color : #F1F4F7;
	padding          : 30px;
	text-align: justify;
}

.met_text_block li{
	line-height: 30px;
}

#met_fullScreenImg {
	position : fixed;
	z-index  : -1;
}

/*-------------------------------
	Header
 ------------------------------*/
header.met_content{
	margin-bottom: 0;
}

header .row-fluid{
	margin-bottom: 0;
}

.met_logo {
	margin-top : 0;
	position: absolute;
	height: 100%;
}

.met_logo,
.met_logo img {
	display : inline-block;
}

.met_logo_loading{
	opacity: 0;
}

.met_logo img{
	max-height: 70px;
}

header {
	height : 100px;
	position   : relative;
	z-index    : 2;
}

header aside {
	float      : right;
	margin-top : 34.5px;
}

header aside > a {
	font-size       : 20px;
	display         : block;
	margin-right    : 20px;
	float           : left;
	text-decoration : none;
}

header aside > nav,
header aside > nav ul,
header aside > nav li {
	float      : left;
	list-style : none;
}

header aside > nav ul {
	margin : 0;
}

header nav {
	/*border-left : 1px solid #CED0D6;*/
}

header aside > nav li a {
	margin-left : 16px;
	font-size   : 12px;
	color       : #BDC0C7;
	line-height : 18px;
}

/*-------------------------------
	Main Menu
 ------------------------------*/
.met_main_nav {
	margin-bottom : 30px;
	z-index       : 4;
	position      : relative;
}

.met_main_nav a:hover{
	text-decoration: none;
}

.met_main_nav.met_fixed_nav {
	position                    : fixed;
	left                        : 0;
	top                         : 0;
	margin                      : 0;
	-webkit-font-smoothing      : subpixel-antialiased !important;
	-webkit-backface-visibility : hidden;
	-moz-backface-visibility    : hidden;
	-ms-backface-visibility     : hidden;
}

.met_main_nav.met_fixed_nav:before {
	display : none;
}

.met_fixed_nav + div {
	margin-top : 90px;
}

.met_main_nav + div {
	position : relative;
	z-index  : 1;
}

.met_main_nav:before {
	position            : absolute;
	width               : 100%;
	height              : 27px;
	top                 : 100%;
	left                : 0;
	content             : '';
	display             : block;
	background-image    : url('../img/menu-shadow.png');
	background-position : center;
	background-repeat   : no-repeat;
}

.met_main_nav ul {
	margin : 0;
}

.met_main_nav > ul li {
	position : relative;
}

.met_main_nav > ul li ul {
	position           : absolute;
	top                : 100%;
	left               : 0;
	min-width          : 200px;
	background-color   : #46484E;
	padding-top        : 15px;
	padding-bottom     : 15px;
	border-radius      : 0 0 5px 5px;
	display: none;
}
/*
.met_main_nav > ul li:hover > ul {
	opacity            : 1;
	visibility         : visible;
	filter             : alpha(opacity=100);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}*/

.met_main_nav > ul li ul a {
	display         : block;
	color           : #8C9099;
	font-size       : 14px;
	font-weight     : 300;
	text-decoration : none;
	padding         : 0 19px;
	height          : 35px;
	line-height     : 35px;
	white-space: nowrap;
}

.met_main_nav ul,
.met_main_nav li {
	list-style : none;
	float      : left;
	display    : block;
}

.met_main_nav > ul > li ul li {
	width : 100%;
}

.met_main_nav > ul > li ul li a {
	border-bottom      : 1px solid rgba(255, 255, 255, 0.05);
	-webkit-transition : color ease 0.3s;
	-moz-transition    : color ease 0.3s;
	-ms-transition     : color ease 0.3s;
	-o-transition      : color ease 0.3s;
}

.met_main_nav > ul > li ul li a:hover {
	color : #FFFFFF;
}

.met_main_nav > ul > li ul li:last-child a {
	border-bottom : none;
}

.met_main_nav .met_menu_home {
	background-color : #5B5E65;
	font-size        : 25px;
	/*padding          : 0 19px;*/
}

.met_main_nav a {
	line-height : 60px;
	padding     : 0 10px;
	color       : #FFFFFF;
	display     : inline-block;
}

.met_has_lower > a:after {
	position    : absolute;
	font-family : FontAwesome;
	right       : 10px;
	content     : '\f105';
	font-size   : 12px;
	color       : #8C9099;
	line-height : 36px;
	height      : 36px;
}

.met_has_lower > a:hover:after {
	color : #FFFFFF;
}

.met_main_nav > ul li ul li ul {
	left : 100%;
	top  : 0;
}

.met_main_nav > ul > li > ul > li > ul {
	left: 105%;
	border-radius: 10px;
}

.met_main_nav > ul > li > ul > li > ul:before{
	content: '';
	display: block;
	position: absolute;
	width: 5%;
	height: 100%;
	background: transparent;
	top: 0;
	left: -5%;
}

.met_main_nav > ul > li > ul > li > ul:after{
	content: '';
	display: block;
	position: absolute;
	top: 10px;
	left: -10.5px;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 10px 15px 10px 0;
	border-color: transparent #46484E transparent transparent;
}

.met_menu_search_wrapper {
	position           : absolute;
	right              : 0;
	top                : 0;
	z-index            : 6;
	width              : 60px;
	height             : 60px;
	overflow           : hidden;
	-webkit-transition : width ease-in 0.3s;
	-moz-transition    : width ease-in 0.3s;
	-ms-transition     : width ease-in 0.3s;
	-o-transition      : width ease-in 0.3s;
}

.met_menu_search_wrapper:hover {
	width : 265px;
}

.met_menu_search {
	width    : 265px;
	height   : 60px;
	position : absolute;
	right    : 0;
	top      : 0;
}

.met_menu_search .met_menu_search_text {
	float      : left;
	width      : 163px;
	border     : none;
	background : none;
	color      : white;
	padding    : 22px 15px;
	margin     : 0;
	outline    : none;
}

.met_menu_search .met_menu_search_text:focus {
	outline : none;
}

.met_menu_search .met_menu_search_text::-webkit-input-placeholder {
	color : #FFFFFF;
}

.met_menu_search .met_menu_search_text::-moz-placeholder {
	color : #FFFFFF;
}

.met_menu_search .met_menu_search_text:-ms-input-placeholder {
	color : #FFFFFF;
}

.met_menu_search .met_menu_search_text:-moz-placeholder {
	color : #FFFFFF;
}

.met_menu_search .met_menu_search_submit {
	float       : right;
	width       : 60px;
	height      : 60px;
	cursor      : pointer;
	text-align  : center;
	color       : #FFFFFF;
	line-height : 60px;
	font-size   : 20px;
}

/*-------------------------------
	Slider
 ------------------------------*/
.met_slider_wrap {
	position : relative;
}

.met_slider_overlay {
	position   : absolute;
	width      : 100%;
	height     : 100%;
	left       : 0;
	top        : 0;
	z-index    : 5;
	background : #FFFFFF url('../img/slider_loader.gif') center center no-repeat;
}

.met_slider_wrap .caroufredsel_wrapper {
	z-index : 1;
}

.met_slider {
	height   : 500px;
	overflow : hidden;
}

.met_slider_item {
	position : relative;
	float    : left;
}

.met_slider_item_preview {
	width : 74.358974359%;
	float : left;
}

.met_slider_item_preview a,
.met_slider_item_preview img {
	display : block;
}

.met_slider_item_caption {
	margin-left : 74.358974359%;
	position    : absolute;
	height      : 100%;
}

.met_slider_item_caption > div {
	padding : 30px;
}

h2.met_title_stack {
	color       : #FFFFFF;
	font-size   : 24px;
	margin      : 0;
	line-height : 27px;
}

h3.met_title_stack {
	color       : #FFFFFF;
	font-size   : 24px;
	margin      : 0;
	line-height : 27px;
}

.met_slider_nav_prev,
.met_slider_nav_next {
	width       : 30px;
	height      : 30px;
	position    : absolute;
	bottom      : 30px;
	right       : 30px;
	display     : block;
	line-height : 32px;
	text-align  : center;
	z-index     : 2;
}

.met_slider_nav_prev {
	right : 70px;
}

.met_slider_nav_next:hover,
.met_slider_nav_prev:hover {
	text-decoration : none;
}

/*-------------------------------
	Image With Text Boxes
 ------------------------------*/
.met_img_with_text {
	position : relative;
}

.met_img_with_text_preview {
	float    : left;
	position : relative;
	width    : 47.3684210526%;
}

.met_img_with_text_preview img {
	display : block;
	width   : 100%;
}

.met_img_with_text_overlay {
	position           : absolute;
	width              : 100%;
	height             : 100%;
	top                : 0;
	left               : 0;
	visibility         : hidden;
	opacity            : 0;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-webkit-transition : all ease 0.5s;
	-moz-transition    : all ease 0.5s;
	-ms-transition     : all ease 0.5s;
	-o-transition      : all ease 0.5s;
	text-align         : center;
}

.met_img_with_text_overlay a {
	display            : inline-block;
	width              : 50px;
	height             : 50px;
	line-height        : 50px;
	border-radius      : 50px;
	font-size          : 20px;
	text-decoration    : none;
	border             : 5px solid rgba(255, 255, 255, 0.1);
	visibility         : hidden;
	opacity            : 0;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	position           : absolute;
	left               : 0;
	top                : 0;
	-webkit-transition : all ease 0.5s;
	-moz-transition    : all ease 0.5s;
	-ms-transition     : all ease 0.5s;
	-o-transition      : all ease 0.5s;

}

.met_img_with_text:hover .met_img_with_text_overlay {
	visibility : visible;
	opacity    : 1;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_img_with_text:hover .met_img_with_text_overlay a {
	visibility : visible;
	opacity    : 1;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
	left       : 42%;
	top        : 40%;
}

.met_img_with_text article {
	position    : absolute;
	margin-left : 47.3684210526%;
	height      : 100%;
}

.met_img_with_text article:before {
	position     : absolute;
	left         : -18px;
	top          : 20%;
	content      : '';
	display      : block;
	width        : 0px;
	height       : 0px;
	border-style : solid;
	border-width : 0 18px 18px 0;
}

.met_img_with_text article.met_bgcolor5:before {
	border-color : transparent #A4AEB9 transparent transparent;
}

.met_img_with_text article.met_bgcolor6:before {
	border-color : transparent #656870 transparent transparent;
}

.met_img_with_text article div {
	padding : 30px;
}

/*-------------------------------
	Tab Content with Icons
 ------------------------------*/
.met_icon_tabs nav a {
	color              : #FFFFFF;
	float              : left;
	display            : block;
	width              : 113px;
	font-size          : 32px;
	line-height        : 70px;
	height             : 70px;
	text-align         : center;
	text-decoration    : none;
	background-color   : inherit;
	-webkit-transition : background-color ease 0.4s;
	-moz-transition    : background-color ease 0.4s;
	-ms-transition     : background-color ease 0.4s;
	-o-transition      : background-color ease 0.4s;
}

.met_icon_tabs nav a:hover {
	text-decoration : none;
}

.met_icon_tabs nav a.met_active_tab {
	background-color : rgba(241, 244, 247, 0.5);
	color            : #656870;
}

.met_icon_tabs article {
	padding            : 30px;
	background-color   : rgba(241, 244, 247, 0.5);
	display            : none;
	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}

.met_icon_tabs article.met_open_tab {
	display            : block;
	-webkit-transition : all ease 0s;
	-moz-transition    : all ease 0s;
	-ms-transition     : all ease 0s;
	-o-transition      : all ease 0s;
}

.met_icon_tabs article > h2 {
	margin-top : 0;
}

.met_icon_tabs_descrs {
	position : relative;
}

/*-------------------------------
	Pagers Near Title
 ------------------------------*/
.met_title_with_pager {
	display : block;
}

.met_title_with_pager nav {
	float : right;

}

.met_title_with_pager nav a {
	color       : #A4AEB9;
	font-size   : 10px;
	display     : block;
	float       : left;
	margin      : 0 2px;
	line-height : 32px;
}

.met_title_with_pager nav a.selected {
	color : #18ADB5;
}

.met_title_with_pager nav a:hover {
	text-decoration : none;
}

/*-------------------------------
	Latest Posts Widget
 ------------------------------*/
.met_latest_posts_item {
	padding-bottom : 10px;
	margin-bottom  : 10px;
	border-bottom  : 1px solid #DCE0E3;
}

.met_latest_post_image,
.met_latest_post_image img {
	display : block;
	float   : left;
	width   : 50px;
	height  : 50px;
}

.met_latest_post_title,
.met_latest_post_descr {
	margin-left   : 60px;
	display       : block;
	font-size     : 12px;
	margin-bottom : 0;
}

.met_latest_post_title {
	font-weight   : 700;
	line-height   : 12px;
	margin-bottom : 5px;
}

.met_latest_post_descr {
	line-height : 17px;
}

.met_latest_posts_block {
	float    : left;
	overflow : hidden;
}

.met_latest_posts_block .met_latest_posts_item:last-child {
	border-bottom : none;
}

/*-------------------------------
	Recent Works Widget
 ------------------------------*/
.met_recent_works {
	height   : 270px;
	overflow : hidden;
}

.met_recent_work_item {
	float         : left;
	position      : relative;
	overflow      : hidden;
	margin-bottom : 10px;
}

.met_recent_work_image,
.met_recent_work_image img {
	display : block;
	float   : left;
}

.met_recent_work_image {
	position : relative;
	z-index  : 1;
}

.met_recent_work_overbox {
	position           : absolute;
	width              : 100%;
	height             : 100%;
	z-index            : 2;
	left               : 0;
	top                : 0;
	opacity            : 0;
	visibility         : hidden;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}

.met_recent_work_item:hover .met_recent_work_overbox {
	opacity    : 1;
	visibility : visible;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_recent_work_overbox a {
	display    : block;
	text-align : center;
	float      : left;
	width      : 100%;
	position   : relative;
	top        : 28%;
}

.met_recent_work_overbox a:hover {
	text-decoration : none;
}

.met_recent_work_overbox span {
	font-weight : 700;
	display     : block;
	width       : 100%;
	text-align  : center;
}

.met_recent_work_overbox i {
	font-size : 23px;
}

/*-------------------------------
	Twitter Widget
 ------------------------------*/
.met_twitter_widget {
	padding : 30px;
}

.met_twitter_item {
	padding    : 20px 0 0 0;
	border-top : 5px solid rgba(0, 0, 0, 0.08);
	margin-top : 20px;
}

.met_twitter_item > i {
	float     : left;
	font-size : 30px;
}

.met_twitter_item > p {
	font-size     : 12px;
	line-height   : 17px;
	margin-left   : 35px;
	margin-bottom : 0;
	display       : block;
}

.met_twitter_item > p > a {
	color: #FFFFFF;
	text-decoration: underline;
}

/*-------------------------------
	Blog Posts List
 ------------------------------*/
.met_dated_blog_posts .met_date {
	font-size   : 24px;
	font-weight : 700;
}

.met_dated_blog_posts .met_month {
	font-size   : 14px;
	font-weight : 700;
}

.met_dated_blog_posts article {
	padding-top : 20px;
	border-top  : 5px solid #DCE0E3;
	margin-top  : 5px;
}

.met_dated_blog_posts article h3 {
	font-size   : 14px;
	font-weight : 700;
	margin-top  : 0;
	line-height : 14px;
}

.met_dated_blog_posts article p {
	font-size     : 12px;
	line-height   : 17px;
	margin-bottom : 0;
}

.met_read_more {
	font-size   : 12px;
	line-height : 14px;
	font-weight : 700;
}

/*-------------------------------
	Twitter Ticker
 ------------------------------*/
.met_twitter_ticker_wrap {
	position   : relative;
	z-index    : 1;
	min-height : 40px;
	overflow   : hidden;
	margin-top : 80px;
}

.met_twitter_ticker_wrap .icon-twitter {
	margin : 7px 7px 4px;
}

.met_twitter_ticker > div {
	float       : left;
	font-weight : 300;
}

.met_twitter_ticker a{
	color: #FFFFFF;
	text-decoration: underline;
}

.met_twitter_ticker_pager {
	float   : right;
	width   : 80px;
	display : table;
}

.met_twitter_ticker_pager a {
	width              : 40px;
	height             : 40px;
	display            : table-cell;
	vertical-align     : middle;
	color              : #FFFFFF;
	text-decoration    : none;
	text-align         : center;
	background-color   : transparent;
	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}

.met_twitter_ticker_pager a:hover {
	background-color : rgba(0, 0, 0, 0.1);
}

.met_news_ticker_wrapper {
	height      : 40px;
	line-height : 40px;
	width       : auto;
	overflow: hidden;
	margin: 0 80px 0 45px;
}

/*-------------------------------
	Footer
 ------------------------------*/
footer {
	padding-top : 32px;
}

footer h3 {
	font-weight : 700;
}

.met_footer_social_icons a {
	font-size          : 20px;
	margin             : 0 10px;
	text-decoration    : none;
	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}

.met_footer_social_icons a:first-child {
	margin-left : 0;
}

.met_footer_copyright {
	height           : 50px;
	line-height      : 50px;
	background-color : #50535A;
	text-decoration  : none;
}

.met_footer_copyright p {
	font-size   : 11px;
	font-weight : 300;
}

footer h3 {
	font-size     : 24px;
	margin-bottom : 0;
	line-height   : 30px;
}

footer span {
	font-size     : 11px;
	line-height   : 12px;
	margin-bottom : 25px;
	display       : block;
}

.met_footer_menu {
	float      : right;
	margin     : 0;
	list-style : none;
	margin-top : 42.5px;
}

.met_footer_menu li {
	list-style   : none;
	float        : left;
	margin-right : 20px;
}

.met_footer_menu li:last-child {
	margin-right : 0;
}

.met_footer_menu li a {
	font-size   : 12px;
	font-weight : 300;
}

footer > .met_content {
	margin-bottom : 42.5px;
	margin-top    : 7px;
}

/*-------------------------------
	Page Header
 ------------------------------*/
.met_page_header {
	min-height : 90px;
}

.met_page_header h1 {
	line-height : 90px;
	height      : 90px;
	padding     : 0 30px;
	display     : inline-block;
	margin      : 0;
	font-weight : 700;
	min-width   : 210px;
}

.met_page_header h2 {
	line-height: 90px;
	height: 87px;
	padding: 0 30px;
	font-weight: 300;
	font-size: 24px;
	display: inline-block;
	margin: 0;
	position: relative;
	-ms-word-wrap: break-word;
	word-wrap: break-word;
	max-width: 836px;
}

.met_page_header h2:before {
	position     : absolute;
	left         : -21px;
	top          : 40%;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 0 18px 18px 0;
	border-color : transparent #A4AEB9 transparent transparent;
}

.met_page_header ul {
	float      : right;
	list-style : none;
	margin     : 38px 30px 0 0;
}

.met_page_header li {
	list-style    : none;
	float         : left;
	padding-right : 20px;
	position      : relative;
}

.met_page_header li:last-child {
	padding-right : 0;
}

.met_page_header li:after {
	display  : block;
	position : absolute;
	content  : '/';
	color    : #FFFFFF;
	right    : 9px;
	top      : 0;
}

.met_page_header li:last-child:after {
	content : '';
}

/*-------------------------------
	Team Members
 ------------------------------*/
.met_team_member {
	margin-right : 30px;
}

.met_team_member img {
	display  : block;
	width    : 100%;
	position : relative;
	z-index  : 1;
}

.met_team_member:last-child {
	margin-right : 0;
}

.met_team_member_details {
	padding  : 30px;
	position : relative;
}

.met_team_member p {
	font-size   : 12px;
	line-height : 17px;
	margin      : 20px 0 0 0;
}

.met_team_member_details:before {
	position     : absolute;
	top          : -18px;
	right        : 14%;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 0 0 18px 18px;
	border-color : transparent transparent #656870 transparent;
}

.met_team_member_socials,
.met_team_member_socials a {
	-webkit-transition : all ease 0.3s;
	-moz-transition    : all ease 0.3s;
	-ms-transition     : all ease 0.3s;
	-o-transition      : all ease 0.3s;
}

.met_team_member_socials {
	height : 30px;
}

.met_team_member_socials a {
	display         : block;
	float           : left;
	width           : 50px;
	opacity         : 0;
	visibility      : hidden;
	filter          : alpha(opacity=0);
	-ms-filter      : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	line-height     : 50px;
	font-size       : 20px;
	text-decoration : none;
	text-align      : center;
}

.met_team_member:hover .met_team_member_socials {
	height : 50px;
}

.met_team_member:hover .met_team_member_socials a {
	opacity    : 100;
	visibility : visible;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_team_member_preview {
	position : relative;
}

.met_team_member_overlay {
	position           : absolute;
	width              : 100%;
	height             : 85%;
	left               : 0;
	top                : 0;
	padding-top        : 15%;
	z-index            : 2;
	opacity            : 0;
	visibility         : hidden;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-webkit-transition : all ease 0.3s;
	-moz-transition    : all ease 0.3s;
	-ms-transition     : all ease 0.3s;
	-o-transition      : all ease 0.3s;
}

.met_team_member_skill {
	background-color : rgba(255, 255, 255, 0.3);
	text-align       : center;
	margin-bottom    : 20px;
	height           : 30px;
	overflow         : hidden;
}

.met_team_member_skill > div {
	display    : inline-block;
	height     : 30px;
	text-align : center;
}

.met_team_member_skill > div > span {
	display            : inline-block;
	width              : 0;
	overflow           : hidden;
	height             : 30px;
	line-height        : 30px;
	color              : rgba(255, 255, 255, 0);
	-webkit-transition : width ease 1s, color ease 0.3s 1s;
	-moz-transition    : width ease 1s, color ease 0.3s 1s;
	-ms-transition     : width ease 1s, color ease 0.3s 1s;
	-o-transition      : width ease 1s, color ease 0.3s 1s;
}

.met_team_member:hover .met_team_member_overlay {
	opacity    : 1;
	visibility : visible;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_team_member:hover .met_team_member_overlay span {
	width : 100%;
	color : #FFFFFF;
}

/*-------------------------------
	Cacoon Sidebar
 ------------------------------*/
.met_cacoon_sidebar {
	padding  : 30px;
	position : relative;
}

.met_cacoon_sidebar:before {
	position     : absolute;
	left         : -18px;
	top          : 5%;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 0 18px 18px 0;
	border-color : transparent #656870 transparent transparent;
}

.met_cacoon_sidebar_item .met_dated_blog_posts article {
	border-top-color : #787B84;
}

.met_cacoon_sidebar_item {
	margin-top : 30px;
}

/*-------------------------------
	Service Box
 ------------------------------*/
.met_service_box > div {
	float            : left;
	width            : 90px;
	height           : 90px;
	background-color : #f1f4f700;
	text-align       : center;
	line-height      : 90px;
	font-size        : 55px;
}

.met_service_box > p {
	margin-left   : 110px;
	margin-bottom : 0;
}

.met_service_box > h2 {
	margin-bottom : 20px;
	margin-top    : 0;
}

/*-------------------------------
	404
 ------------------------------*/
.met_404 {
	margin-top    : 140px;
	margin-bottom : 145px;
}

.met_404_box {
	padding  : 30px;
	position : relative;
}

.met_404_box:after {
	position     : absolute;
	bottom       : -18px;
	left         : 40px;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 18px 18px 0 0;
	border-color : #656870 transparent transparent transparent;
}

.met_404 h2 {
	font-size   : 48px;
	line-height : 74px;
	margin      : 0;
}

.met_404_box h4 {
	font-size   : 18px;
	font-weight : 300;
	line-height : 22px;
	margin      : 0;
}

.met_404_box form {
	position      : relative;
	margin-top    : 35px;
	margin-bottom : 0;
}

.met_404_box input[type="text"] {
	display            : block;
	padding            : 0 19px;
	height             : 40px;
	line-height        : 40px;
	border-radius      : 0;
	box-shadow         : none;
	font-size          : 14px;
	color              : #A3ADB8;
	box-sizing         : border-box;
	-moz-box-sizing    : border-box;
	-ms-box-sizing     : border-box;
	-webkit-box-sizing : border-box;
	-khtml-box-sizing  : border-box;
	width              : 100%;
	border             : 1px solid #BABABA;
	margin-bottom      : 0;
}

.met_404_box button {
	background     : none;
	border         : none;
	display        : block;
	position       : absolute;
	right          : 0;
	top            : 0;
	width          : 40px;
	height         : 40px;
	text-align     : center;
	vertical-align : middle;
	font-size      : 15px;
}

/*-------------------------------
	Contact Page
------------------------------*/
#map {
	width  : 100%;
	height : 620px;
}

#map img {
	max-width: none;
	vertical-align: baseline;
}

.met_contact_map_box {
	padding   : 30px;
	max-width : 241px;
	top       : 30px;
	right     : 30px;
	z-index   : 1;
}

.met_contact_map_box h3 {
	margin-top : 0;
}

.met_contact_map_box > div {
	margin-bottom : 20px;
}

.met_contact_map_box > div:last-child {
	margin-bottom : 0;
}

.met_contact_info {
	font-size : 12px;
}

.met_contact_info span {
	min-width : 64px;
	display   : inline-block;
}

.met_contact_socials a {
	font-size       : 20px;
	display         : block;
	margin-right    : 20px;
	float           : left;
	text-decoration : none;
}

.met_contact_form form {
	margin : 0;
}

.met_contact_form input[type="text"],
.met_contact_form input[type="email"],
.met_contact_form textarea {
	border             : none;
	background         : rgba(255, 255, 255, 0.1);
	box-shadow         : none;
	padding            : 0 10px;
	height             : 40px;
	line-height        : 40px;
	width              : 100%;
	display            : block;
	color              : #FFFFFF;
	border-radius      : 0;
	box-sizing         : border-box;
	-moz-box-sizing    : border-box;
	-ms-box-sizing     : border-box;
	-webkit-box-sizing : border-box;
	-khtml-box-sizing  : border-box;
	resize             : none;
}

.met_contact_form input[type="submit"] {
	border             : none;
	float              : right;
	min-width          : 100px;
	text-align         : center;
	font-weight        : 700;
	font-size          : 12px;
	padding            : 7px;
	-webkit-transition : background-color ease 0.4s;
	-moz-transition    : background-color ease 0.4s;
	-ms-transition     : background-color ease 0.4s;
	-o-transition      : background-color ease 0.4s;
}

.met_contact_form input[type="submit"]:hover {
	background-color : rgba(255, 255, 255, 0.1);
}

.met_contact_thank_you {
	display    : none;
	text-align : center;
	margin     : 20px 0;
}

/*-------------------------------
	Blog
------------------------------*/
.met_blog_list_preview {
	display  : block;
	position : relative;
}

.met_blog_list_preview img {
	display : block;
}

.met_blog_list_preview .met_blog_video_iframe {
	display     : block;
	margin-left : 100px;
}

.met_blog_video_iframe iframe {
	display : block;
}

.met_blog_list_preview aside {
	position : absolute;
	width    : 100px;
	height   : 100%;
	top      : 0;
	left     : 0;
}

.met_blog_list_preview aside:after {
	position     : absolute;
	right        : -18px;
	top          : 40px;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 18px 18px 0 0;
	border-color : #18ADB5 transparent transparent transparent;
}

.met_blog_list_preview aside div {
	padding : 20px;
}

.met_blog_list_preview aside span:first-child {
	font-size   : 60px;
	margin-top  : 10px;
	line-height : 60px;
	display     : block;
	text-align  : center;
	font-weight : 700;
}

.met_blog_list_preview aside span:last-child {
	font-size   : 24px;
	line-height : 24px;
	display     : block;
	text-align  : center;
	font-weight : 700;
}

.met_blog_title {
	margin  : 20px 0 10px;
	display : block;
}

.met_blog_title h2 {
	margin : 0;
}

.met_blog_miscs {
	margin-top : 20px;
	border-top : 5px solid #787B84;
}

.met_blog_socials,
.met_blog_posted_by {
	float      : left;
	margin-top : 6px;
}

.met_blog_socials a {
	font-size    : 20px;
	margin-right : 15px;
	color        : #656870;
}

.met_blog_socials a:last-child {
	margin-right : 0;
}

.met_blog_posted_by {
	font-weight  : 300;
	font-size    : 12px;
	line-height  : 20px;
	margin-left  : 20px;
	border-left  : 1px solid #BDC0C7;
	color        : #BDC0C7;
	padding-left : 20px;
}

.met_blog_comment_count {
	float            : right;
	display          : block;
	background-color : #787B84;
	border-left      : 10px solid #18ADB5;
	font-size        : 12px;
	line-height      : 30px;
	height           : 30px;
	font-weight      : 300;
	padding          : 0 10px;
}

.met_blog_categories {
	padding : 30px;
}

.met_blog_categories a {
	font-weight : 700;
	font-size   : 14px;
	color       : #FFFFFF;
	display     : block;
	line-height : 18px;
	padding-top : 20px;
	margin-top  : 20px;
	border-top  : 5px solid #787B84;
}

.met_blog_slider_wrap {
	position : relative;
	float    : left;
	overflow : hidden;
	width    : 100%;
}

.met_blog_slider {
	float    : left;
	width    : 100%;
	overflow : hidden;
	height   : 300px;
}

.met_blog_slider a,
.met_blog_slider img {
	display : block;
	float   : left;
}

.met_blog_slider_nav_prev,
.met_blog_slider_nav_next {
	position      : absolute;
	width         : 50px;
	height        : 50px;
	border-radius : 5px;
	background    : rgba(0, 0, 0, 0.7);
	color         : #FFFFFF;
	text-align    : center;
	font-size     : 20px;
}

.met_blog_slider_nav_prev {
	left               : -50px;
	top                : 40%;
	-webkit-transition : left ease 0.3s;
	-moz-transition    : left ease 0.3s;
	-ms-transition     : left ease 0.3s;
	-o-transition      : left ease 0.3s;
}

.met_blog_slider_nav_next {
	right              : -50px;
	top                : 40%;
	-webkit-transition : right ease 0.3s;
	-moz-transition    : right ease 0.3s;
	-ms-transition     : right ease 0.3s;
	-o-transition      : right ease 0.3s;
}

.met_blog_slider_wrap:hover .met_blog_slider_nav_prev {
	left : 25px;
}

.met_blog_slider_wrap:hover .met_blog_slider_nav_next {
	right : 25px;
}

.met_blog_slider_nav_prev i,
.met_blog_slider_nav_next i {
	position : absolute;
	left     : 18px;
	top      : 17px;
}

.met_blog_slider_nav_prev:hover,
.met_blog_slider_nav_next:hover {
	text-decoration : none;
}

/*-------------------------------
	Flickr Widget
------------------------------*/
.met_flickr_widget {
	position : relative;
}

.met_flickr_widget_title {
	position : absolute;
	height   : 100%;
	height   : calc(100% -5px);
	width    : 160px;
}

.met_flickr_widget_title div {
	padding : 30px;
}

.met_flickr_widget_list {
	margin-left : 160px;
}

.met_flickr_widget_list a {
	margin : 0 0 5px 5px;
}

.met_flickr_widget_list a,
.met_flickr_widget_list img {
	display : block;
	float   : left;
}

/*-------------------------------
	Comments
------------------------------*/
.met_blog_comments_title {
	font-size   : 24px;
	font-weight : 700;
	line-height : 27px;
	padding     : 16px 30px;
	margin      : 0 0 35px 0;
	display     : block;
	position    : relative;
}

.met_blog_comments_title:before {
	position     : absolute;
	bottom       : -18px;
	left         : 82px;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 18px 18px 0 0;
	border-color : #18ADB5 transparent transparent transparent;
}

.met_comment_box {
	float         : left;
	width         : 100%;
	margin-bottom : 20px;
}

.met_comment_box:last-child {
	margin-bottom : 0;
}

.met_comment_box > .met_comment {
	margin-top       : 20px;
	background-color : #F8F9FB;
}

.met_comment_box > .met_comment:first-child {
	margin-top : 0;
}

.met_comment_box > .met_comment > img {
	display : block;
	float   : left;
	width   : 120px;
	height  : 90px;
}

.met_comment_box > .met_comment > .met_comment_descr {
	margin-left  : 140px;
	margin-right : 127px;
	position     : relative;
	padding      : 20px 0;
}

.met_comment_box > .met_comment > .met_comment_descr > h5,
.met_comment_box > .met_comment > .met_comment_descr > span {
	display      : inline-block;
	margin-right : 20px;
}

.met_comment_box > .met_comment > .met_comment_descr > span {
	color        : #839799;
	float        : right;
	font-size    : 12px;
	margin-right : 0;
}

.met_comment_box > .met_comment > .met_comment_descr > h5 {
	margin-top  : 0;
	font-size   : 14px;
	line-height : 15px;
	font-weight : 700;
}

.met_comment_box > .met_comment > .met_comment_descr > p {
	margin-bottom : 0;
	line-height   : 18px;
}

.met_comment_edit_link {
	display   : block;
	float     : right;
	margin    : 0 5px;
	font-size : 12px;
}

.met_comment_reply_link {
	display     : block;
	float       : right;
	font-size   : 14px;
	font-weight : 700;
	width       : 107px;
	height      : 90px;
	line-height : 90px;
	text-align  : center;
}

.met_comment_awaiting_moderation {
	float      : left;
	font-style : italic;
	font-size  : 12px;
	color      : #839799;
}

.met_comment_box > .met_comment {
	margin-left : 160px;
}

.met_comment_box > .met_comment:nth-child(1) {
	margin-left : 0;
}

.met_comment_box > .met_comment:nth-child(2) {
	margin-left : 120px;
}

.met_comment_box > .met_comment:nth-child(3) {
	margin-left : 240px;
}

.met_comment_box > .met_sub_comment {
	position : relative;
}

.met_comment_box > .met_sub_comment:before {
	position         : absolute;
	left             : -74px;
	top              : -20px;
	content          : '';
	width            : 64px;
	height           : 80px;
	background-image : url('../img/comment_child.png');
	display          : block;
}

.met_post_comment {
	float   : left;
	width   : 100%;
	display : block;
}

.met_leave_a_reply {
	margin    : 15px 0;
	font-size : 24px;
}

.met_leave_a_reply.met_no_margin_top {
	margin-top : 0;
}

.met_leave_a_reply_subtitle {
	margin-top    : -20px;
	display       : block;
	float         : left;
	margin-bottom : 25px;
	width         : 100%;
}

.met_comment_form input[type="text"],
.met_comment_form input[type="email"],
.met_comment_form .met_textarea {
	background : #F8F9FB;
	color      : #555555;
}

.met_comment_form .met_textarea{
	height: 100px;
}

.met_comment_form button {
	padding-right : 25px;
	padding-left  : 25px;
	border        : none;
}

.met_comment_form button:after {
	display : none;
}

/*-------------------------------
	Pagination
 ------------------------------*/
.met_pagination {
	margin-bottom : 0;
}

.met_pagination ul {
	box-shadow    : none;
	border-radius : 0;
}

.met_pagination > ul > li > a {
	border             : none;
	background-color   : #0F1010;
	color              : #D8D8D8;
	margin             : 0 1px;
	padding            : 7px 12px;
	-webkit-transition : all ease 0.3s;
	-moz-transition    : all ease 0.3s;
	-ms-transition     : all ease 0.3s;
	-o-transition      : all ease 0.3s;
}

.met_pagination > ul > li > a:hover,
.met_pagination > ul > li > a.active {
	background-color : #D8D8D8;
	color            : #0F1010;
}

.met_pagination > ul > li > a:first-child,
.met_pagination > ul > li > a:last-child {
	border-radius : 0;
}

/*-------------------------------
	Portfolio List
 ------------------------------*/
.met_portfolio_row {
}

.met_portfolio_row .span6,
.met_portfolio_row .span4 {
	margin-bottom : 30px;
}

.met_portfolio_row .span6:nth-child(2n + 1),
.nth-child-2np1 {
	margin-left : 0 !important;
	clear       : both;
}

.met_portfolio_row .span4:nth-child(3n + 1),
.nth-child-3np1 {
	margin-left : 0 !important;
	clear       : both;
}

.met_portfolio_item_preview,
.met_portfolio_item_preview img {
	display : block;
	float   : left;
	width   : 100%;
}

.met_portfolio_item_preview_wrap {
	position : relative;
}

.met_portfolio_item_overlay {
	position           : absolute;
	left               : 0;
	top                : 0;
	width              : 100%;
	height             : 100%;
	opacity            : 0;
	visibility         : hidden;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-webkit-transition : all ease 0.5s;
	-moz-transition    : all ease 0.5s;
	-ms-transition     : all ease 0.5s;
	-o-transition      : all ease 0.5s;
	text-align         : center;
	overflow           : hidden;
}

.met_portfolio_item_overlay a {
	position           : absolute;
	display            : inline-block;
	width              : 50px;
	height             : 50px;
	line-height        : 50px;
	text-align         : center;
	text-decoration    : none;
	-webkit-transition : all ease 0.5s;
	-moz-transition    : all ease 0.5s;
	-ms-transition     : all ease 0.5s;
	-o-transition      : all ease 0.5s;
}

.met_portfolio_item_overlay a:nth-child(1) {
	top  : 100%;
	left : 35%;
}

.met_portfolio_item_overlay a:nth-child(2) {
	left : 57%;
	top  : -100%;
}

.met_portfolio_row > div:hover .met_portfolio_item_overlay {
	opacity    : 1;
	visibility : visible;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_portfolio_row > div:hover .met_portfolio_item_overlay a {
	top : 42%;
}

.lightbox-images {
	display : none;
}

.met_portfolio_item_details {
	position : relative;
	float    : left;
	width    : 100%;
}

.met_portfolio_item_details:before {
	position     : absolute;
	top          : -18px;
	left         : 40px;
	content      : '';
	display      : block;
	width        : 0;
	height       : 0;
	border-style : solid;
	border-width : 18px 0 0 18px;
	border-color : transparent transparent transparent #656870;
}

.met_portfolio_item_descr {
	float : left;
	width : 75%;
}

.met_portfolio_item_descr div {
	padding : 30px;
}

.met_portfolio_item_descr div h3,
.met_portfolio_item_descr div p {
	margin : 0;
}

.met_portfolio_item_descr a:hover {
	text-decoration : none;
}

.met_portfolio_item_descr p {
	font-size   : 13px;
	line-height : 17px;
}

.met_portfolio_item_share {
	width              : 25%;
	height             : 100%;
	right              : 0;
	top                : 0;
	position           : absolute;
	display            : -webkit-box;
	display            : -ms-flexbox;
	display            : -moz-box;
	display            : box;
	-webkit-box-align  : center;
	-moz-box-align     : center;
	box-align          : center;
	-webkit-box-pack   : center;
	-moz-box-pack      : center;
	box-pack           : center;
	-ms-flex-align     : center;
	-webkit-box-orient : horizontal;
	-moz-box-orient    : horizontal;
}

.met_portfolio_item_share:hover {
	text-decoration : none;
}

.met_portfolio_item_share > span {
	text-align         : center;
	width              : 100%;
	display            : block;
	position           : relative;
	width              : 100%;
	height             : 100%;
	display            : -webkit-box;
	display            : -ms-flexbox;
	display            : -moz-box;
	display            : box;
	-webkit-box-align  : center;
	-moz-box-align     : center;
	box-align          : center;
	-webkit-box-pack   : center;
	-moz-box-pack      : center;
	box-pack           : center;
	-ms-flex-align     : center;
	-webkit-box-orient : horizontal;
	-moz-box-orient    : horizontal;

	-ms-flex-align: center;
	-ms-flex-pack: center;
}

.met_portfolio_item_share > span {
	font-size          : 24px;
	font-weight        : 700;
	opacity            : 1;
	visibility         : visible;
	filter             : alpha(opacity=100);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}

.met_portfolio_item_share:hover > span {
	opacity    : 0;
	visibility : hidden;
	filter     : alpha(opacity=0);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
}

.met_portfolio_item_socials {
	width              : 100%;
	height             : 100%;
	position           : absolute;
	left               : 0;
	top                : 0;
	opacity            : 0;
	visibility         : hidden;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	margin-top         : 38%;

	-webkit-transition : all ease 0.4s;
	-moz-transition    : all ease 0.4s;
	-ms-transition     : all ease 0.4s;
	-o-transition      : all ease 0.4s;
}

.span4 .met_portfolio_item_socials {
	margin-top : 66%;
}

.met_portfolio_item_share:hover > .met_portfolio_item_socials {
	opacity    : 1;
	visibility : visible;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_portfolio_item_socials div {
	text-align : center;
	width      : 100%;
	font-size  : 25px;
}

.met_portfolio_item_socials div a {
	margin-right : 10px;
}

.met_portfolio_item_socials div a:last-child {
	margin-right : 0;
}

.met_portfolio_item_socials div a:hover {
	text-decoration : none;
}

.met_portfolio_item_slider_wrap {
	position : relative;
	float    : left;
	overflow : hidden;
	width    : 100%;
}

.met_portfolio_item_slider {
	float    : left;
	width    : 100%;
	overflow : hidden;
}

.span6 .met_portfolio_item_slider {
	height : 300px;
}

.span4 .met_portfolio_item_slider {
	height : 195px;
}

.met_portfolio_item_slider a,
.met_portfolio_item_slider img {
	display : block;
	float   : left;
}

.met_portfolio_item_slider_nav_prev,
.met_portfolio_item_slider_nav_next {
	position   : absolute;
	width      : 35px;
	height     : 75px;
	background : rgba(0, 0, 0, 0.7);
	color      : #FFFFFF;
	text-align : center;
	font-size  : 20px;
}

.met_portfolio_item_slider_nav_prev {
	left               : -35px;
	top                : 40%;
	-webkit-transition : left ease 0.3s;
	-moz-transition    : left ease 0.3s;
	-ms-transition     : left ease 0.3s;
	-o-transition      : left ease 0.3s;
}

.met_portfolio_item_slider_nav_next {
	right              : -35px;
	top                : 40%;
	-webkit-transition : right ease 0.3s;
	-moz-transition    : right ease 0.3s;
	-ms-transition     : right ease 0.3s;
	-o-transition      : right ease 0.3s;
}

.met_portfolio_item_slider_wrap:hover .met_portfolio_item_slider_nav_prev {
	left : 0;
}

.met_portfolio_item_slider_wrap:hover .met_portfolio_item_slider_nav_next {
	right : 0;
}

.met_portfolio_item_slider_nav_prev i,
.met_portfolio_item_slider_nav_next i {
	position : absolute;
	left     : 11px;
	top      : 29px;
}

.met_portfolio_item_slider_nav_prev:hover,
.met_portfolio_item_slider_nav_next:hover {
	text-decoration : none;
}

.met_portfolio_service_box,
.met_portfolio_tag_box,
.met_portfolio_share_box {
	margin-bottom : 20px;
}

.met_portfolio_service_box h3,
.met_portfolio_tag_box h3,
.met_portfolio_share_box h3 {
	margin-top : 0;
}

.met_portfolio_service {
	display : block;
}

.met_portfolio_tag_box a {
	color : #65676F;
}

.met_portfolio_tag_box a.met_tag_size1 {
	font-size : 12px;
}

.met_portfolio_tag_box a.met_tag_size3 {
	font-size : 18px;
}

.met_portfolio_share_box a {
	font-size    : 20px;
	margin-right : 15px;
	color        : #65676F;
}

.met_portfolio_share_box a:last-child {
	margin-right : 0;
}

.met_portfolio_posted_by {
	margin-top  : 30px;
	font-size   : 12px;
	font-weight : 300;
}

.met_portfolio_detail_box {
	position : relative;
}

.met_portfolio_detail_box:before {
	position         : absolute;
	content          : '';
	left             : -45px;
	top              : 0;
	background-color : #DCE0E3;
	width            : 5px;
	height           : 100%;
}

.met_portfolio_detail_box h2 {
	margin-top : 0;
}

.met_portfolio_detail_box .met_align_left {
	float  : left;
	margin : 5px 5px 5px 0
}

.met_portfolio_detail_box .met_align_right {
	float  : right;
	margin : 5px 0 5px 5px
}

/*-------------------------------
	Skill Circles
 ------------------------------*/
.met_skill_row {
	margin : 60px 0;
}

.dial_wrap {
	text-align : center;
}

.dial {
	display     : none;
	color       : #373737 !important;
	font-size   : 50px !important;
	font-weight : 800 !important;
	font-family : inherit !important;
	width       : 100px !important;
	height      : 60px !important;
	top         : 23% !important;
	left        : 21% !important;
	margin      : 0 !important;
}

.dial.visible {
	display : block;
}

.knob {
	display    : inline-block;
	position   : relative;
	text-align : center;
	width      : 170px;
	min-height : 170px;
}

.met_responsive_nav {
	display               : none;
	background-image      : url('../img/responsive-nav-bg.png');
	background-position   : center right;
	background-repeat     : no-repeat;
	display               : none;
	width                 : 100%;
	-webkit-appearance    : none;
	background-color      : #F8F8F8;
	border                : 1px solid #E8E8E8;
	-webkit-border-radius : 0;
	-moz-border-radius    : 0;
	border-radius         : 0;
	-webkit-box-shadow    : none;
	-moz-box-shadow       : none;
	box-shadow            : none;
	color                 : #8F8F8F;
	padding               : 0 10px;
	border-radius         : 0;
	line-height           : 14px;
	float                 : left;
}

/*-------------------------------
	Background Images and Patterns
------------------------------*/
.bgpattern1 {
	background-image : url("../img/bgpatterns/bgimage1.jpg");
}

.bgpattern2 {
	background-image : url("../img/bgpatterns/bgimage2.jpg");
}

.bgpattern3 {
	background-image : url("../img/bgpatterns/bgimage3.jpg");
}

.bgpattern4 {
	background-image : url("../img/bgpatterns/bgimage4.jpg");
}

.bgpattern5 {
	background-image : url("../img/bgpatterns/bgimage5.jpg");
}

.bgpattern6 {
	background-image : url("../img/bgpatterns/bgimage6.jpg");
}

.bgpattern7 {
	background-image : url("../img/bgpatterns/bgimage7.jpg");
}

.bgpattern8 {
	background-image : url("../img/bgpatterns/bgimage8.jpg");
}

.bgpattern9 {
	background-image : url("../img/bgpatterns/bgimage9.jpg");
}

.bgpattern10 {
	background-image : url("../img/bgpatterns/bgimage10.jpg");
}

.bgpattern11 {
	background-image : url("../img/bgpatterns/bgimage11.jpg");
}

.bgpattern12 {
	background-image : url("../img/bgpatterns/bgimage12.jpg");
}

.bgpattern13 {
	background-image : url("../img/bgpatterns/bgimage13.jpg");
}

.bgpattern14 {
	background-image : url("../img/bgpatterns/bgimage14.jpg");
}

.bgpattern15 {
	background-image : url("../img/bgpatterns/bgimage15.jpg");
}

.bgpattern16 {
	background-image : url("../img/bgpatterns/bgimage16.jpg");
}

.bgpattern17 {
	background-image : url("../img/bgpatterns/bgimage17.jpg");
}

.bgpattern18 {
	background-image : url("../img/bgpatterns/bgimage18.jpg");
}

.bgpattern19 {
	background-image : url("../img/bgpatterns/bgimage19.jpg");
}

.bgpattern20 {
	background-image : url("../img/bgpatterns/bgimage20.jpg");
}

.bgpattern21 {
	background-image : url("../img/bgpatterns/bgimage21.jpg");
}

.bgpattern22 {
	background-image : url("../img/bgpatterns/bgimage22.jpg");
}

.bgpattern23 {
	background-image : url("../img/bgpatterns/bgimage23.jpg");
}

.bgpattern24 {
	background-image : url("../img/bgpatterns/bgimage24.jpg");
}

.bgpattern25 {
	background-image : url("../img/bgpatterns/bgimage25.jpg");
}

.bgpattern26 {
	background-image : url("../img/bgpatterns/bgimage26.jpg");
}

.bgpattern27 {
	background-image : url("../img/bgpatterns/bgimage27.jpg");
}

.bgpattern28 {
	background-image : url("../img/bgpatterns/bgimage28.jpg");
}

.bgimage1 {
	background-image : url("../img/bgimages/thumbs/bgimage1.html");
}

.bgimage2 {
	background-image : url("../img/bgimages/thumbs/bgimage2.html");
}

/*-------------------------------
	Cacoon Slider
------------------------------*/
.met_thumbnail_slider_1_wrap {
	padding: 10px;
	position: relative;
}

.met_thumbnail_slider_1_wrap_loading{
	height: 500px;
	overflow: hidden;
}

.met_thumbnail_slider_1_overlay {
	position   : absolute;
	width      : 100%;
	height     : 100%;
	left       : 0;
	top        : 0;
	z-index    : 5;
	background : #FFFFFF url('../img/slider_loader.gif') center 100px no-repeat;
}

.met_thumbnail_slider_1_big {
	overflow: hidden;
}
.met_thumbnail_slider_1_big img {
	display: block;
}

.met_thumbnail_slider_1_small {
	height: 160px;
	overflow: hidden;
	position: absolute;
	bottom: 10px;
	left: 10px;
}
.met_thumbnail_slider_1_small .met_thumbnail_slider_1_images {
	float: left;
}
.met_thumbnail_slider_1_small img,
.met_thumbnail_slider_1_next {
	background: rgba( 255, 255, 255, 0.2 );
	display: block;
	width: 100px;
	height: 100px;
	padding: 10px;
	margin: 20px 0 20px 20px;
	float: left;
	cursor: pointer;

	-webkit-transition: background 0.6s ease, margin-top ease 0.3s;
	-moz-transition: background 0.6s ease, margin-top ease 0.3s;
	-ms-transition: background 0.6s ease, margin-top ease 0.3s;
	-o-transition: background 0.6s ease, margin-top ease 0.3s;
	transition: background 0.6s ease, margin-top ease 0.3s;
}
.met_thumbnail_slider_1_small img:hover,
.met_thumbnail_slider_1_next:hover {
	background: rgba( 255, 255, 255, 0.3 );
	margin-top: 0;
	text-decoration: none;
}
.met_thumbnail_slider_1_next {
	color: rgba( 0, 0, 0, 0.5 );
	font-size: 120px;
	line-height: 85px;
	text-align: center;
	text-decoration: none;
}

.met_thumbnail_slider_1_effects_left{left: -10px;}
.met_thumbnail_slider_1_effects_right{right: -10px;}

.met_thumbnail_slider_1_effects{
	position: absolute;
}

.met_thumbnail_slider_1_effects.met_thumbnail_slider_1_top{top: 0.3%;}
.met_thumbnail_slider_1_effects.met_thumbnail_slider_1_bottom{top: 2%;}

.met_thumbnail_slider_1_effects_left .met_thumbnail_slider_1_title{padding: 15px 20px 15px 40px;}
.met_thumbnail_slider_1_effects_right .met_thumbnail_slider_1_title{padding: 15px 40px 15px 20px;}

.met_thumbnail_slider_1_title{
	margin-bottom: 20px;
	font-size: 25px;
	font-weight: 600;
	color: #FFF;
	-webkit-transform: skew(-20deg);
	-moz-transform: skew(-20deg);
	-o-transform: skew(-20deg);
	-ms-transform: skew(-20deg);
	transform: skew(-20deg);
}

.met_thumbnail_slider_1_effects_left .met_thumbnail_slider_1_title a {right: -35px;}
.met_thumbnail_slider_1_effects_right .met_thumbnail_slider_1_title a {left: -35px;}

.met_thumbnail_slider_1_title a:after{
	font-family: FontAwesome;
	font-size: 31px;
	color: #FFFFFF;
	position: absolute;
	top: 0;
}

.met_thumbnail_slider_1_effects_left .met_thumbnail_slider_1_title a:after{content: '\f105';left: 11px;}
.met_thumbnail_slider_1_effects_right .met_thumbnail_slider_1_title a:after{content: '\f104';left: 7px;}

.met_thumbnail_slider_1_title a {
	position: absolute;
	top: 0;
	width: 30px;
	height: 100%;
	font-size: 25px;
	text-align: center;
	line-height: 51px;
}

.met_thumbnail_slider_1_effects_left .met_thumbnail_slider_1_subtitle{padding: 15px 20px 15px 40px; left: 0}
.met_thumbnail_slider_1_effects_right .met_thumbnail_slider_1_subtitle{padding: 15px 40px 15px 20px; right: 0}

.met_thumbnail_slider_1_subtitle{
	position: absolute;
	font-size: 16px;
	font-weight: 600;
	color: #FFF;
	display: inline-block;
	-webkit-transform: skew(-20deg);
	-moz-transform: skew(-20deg);
	-o-transform: skew(-20deg);
	-ms-transform: skew(-20deg);
	transform: skew(-20deg);
}

.met_thumbnail_slider_1_subtitle,
.met_thumbnail_slider_1_title{
	z-index: 1;
	margin-top: -20px;
	opacity            : 0;
	visibility         : hidden;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-webkit-transition: all ease 0.3s;
	-moz-transition: all ease 0.3s;
	-ms-transition: all ease 0.3s;
	-o-transition: all ease 0.3s;

}

.met_thumbnail_slider_1_title.met_active_title,
.met_thumbnail_slider_1_subtitle.met_active_title{
	z-index: 2;
	-webkit-transition: all ease 0.3s;
	-moz-transition: all ease 0.3s;
	-ms-transition: all ease 0.3s;
	-o-transition: all ease 0.3s;
	margin-top: 0;
	opacity            : 1;
	visibility         : visible;
	filter             : alpha(opacity=100);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}


/*-------------------------------
	Buttons
------------------------------*/
.btn{
	border-radius: 0;
	padding: 17px 32px;
	font-size: 18px;
	font-weight: 600;
	border: none;
	box-shadow: none;
	-webkit-transition: background-color ease 0.3s;
	-moz-transition: background-color ease 0.3s;
	-ms-transition: background-color ease 0.3s;
	-ms-transition: background-color ease 0.3s;
}

.btn:hover{
	-webkit-transition: background-color ease 0.3s;
	-moz-transition: background-color ease 0.3s;
	-ms-transition: background-color ease 0.3s;
	-ms-transition: background-color ease 0.3s;
}

.btn-large{
	padding-left: 42px;
	padding-right: 42px;
	font-size: 20px;
}

.btn-small{
	padding-left: 40px;
	padding-right: 40px;
	font-size: 12px;
}

.btn-mini{
	padding-left: 23px;
	padding-right: 23px;
	font-size: 11px;
}

.btn-primary{background: #18ADB5;}
.btn-primary:hover{background-color: #5B5E65;}

.btn-info{background: #656870;}
.btn-info:hover{background-color: #5B5E65;}

.btn-warning{background: #A4AEB9;}
.btn-warning:hover{background-color: #5B5E65;}

.btn-danger{background: #B61857;}
.btn-danger:hover{background-color: #5B5E65;}

.btn-success{background: #18B632;}
.btn-success:hover{background-color: #5B5E65;}

.met_div5{
	width: 20%;
	float: left;
}




/*-------------------------------
	Custom Accordion
 ------------------------------*/
.met_accordion .accordion-group {
	border        : 0;
	border-radius : 0;
}

.met_accordion .accordion-toggle {
	color           : #FFFFFF;
	text-decoration : none;
	font-weight: 600;
	font-size: 20px;
}

.met_accordion .accordion-inner {
	border       : none;
	background-color: #F8FAFB;
}

.met_accordion span.icon-white {
	color    : white !important;
	position : relative;
	z-index  : 2;
}

.met_accordion span.met_bgcolor {
	position           : absolute;
	width              : 100%;
	height             : 100%;
	z-index            : 1;
	opacity            : 0;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	left               : 0;
	top                : 0;
	-webkit-transition : opacity ease-out 0.3s;
	-moz-transition    : opacity ease-out 0.3s;
	-ms-transition     : opacity ease-out 0.3s;
	-o-transition      : opacity ease-out 0.3s;
}

.met_accordion .accordion-toggle:hover span.met_bgcolor {
	opacity : 1;
	filter  : alpha(opacity=100);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.met_accordion .accordion-toggle {
	font-size        : 19px;
	padding          : 17px 15px;
	background-color : #18ADB6;
	color            : #FFFFFF;
}

.met_accordion .collapsed.accordion-toggle {
	background-color : #A4AEB9;
}

.met_accordion .accordion-toggle .met_icon_plus {
	float               : right;
	background-image    : url("../img/icon_plus.png");
	background-repeat   : no-repeat;
	background-position : center center;
	width               : 13px;
	height              : 22px;
}

.met_accordion .accordion-toggle .met_icon_minus {
	float               : right;
	background-image    : url("../img/icon_minus.png");
	background-repeat   : no-repeat;
	background-position : center center;
	width               : 10px;
	height              : 22px;
}

@media all and (-webkit-min-device-pixel-ratio: 1.5) {
	.met_accordion .accordion-toggle .met_icon_plus {
		background-image    : url("../img/icon_plus%402x.png");
		background-size: 13px 13px;
	}

	.met_accordion .accordion-toggle .met_icon_minus {
		background-image    : url("../img/icon_minus%402x.png");
		background-size: 10px 3px;
	}
}

/*-------------------------------
	Testimonials
 ------------------------------*/
.met_testimonial {
	float              : left;
	width              : 150px;
	height             : 110px;
	margin-right       : 9px;
	border-bottom: 5px solid transparent;
	cursor: pointer;
	-webkit-transition : all ease 0.2s;
	-moz-transition    : all ease 0.2s;
	-ms-transition     : all ease 0.2s;
	-o-transition      : all ease 0.2s;
}

.met_testimonial:hover{
	border-bottom: 5px solid #18ADB6;
}

.met_testimonial_photo {
	width              : 150px;
	height             : 150px;
	padding            : 0;
	float              : left;
}

.met_testimonial_plus {
	float            : left;
	display          : table;
	width            : 60px;
	height           : 60px;
}

.met_testimonial_plus div {
	display        : table-cell;
	text-align     : center;
	vertical-align : middle;
	color          : #FFFFFF;
}

.met_testimonial_messages {
	text-align : center;
	font-size  : 15px;
}

.met_testimonial_messages div {
	display : none;
	padding : 20px 0;
}


/*-------------------------------
	HTML5 Audio Player
------------------------------*/
.met_audio_player{
	background-color: #A4AEB9;
	height: 50px;
}

.met_audio_play_pause{
	width: 50px;
	height: 50px;
	line-height: 45px;
	border: none;
	border-right: 1px solid #FFFFFF;
	color: #FFFFFF;
	text-align: center;
	font-size: 13px;
	background: none;
}

.met_blog_html5_video video{
	display: block;
	width: 100%;
	max-width: 100%;
	margin-bottom: 1px;
}

.met_audio_current_time{
	width: 45px;
	height: 50px;
	line-height: 50px;
	text-align: right;
	font-size: 12px;
	color: #FFFFFF;
	display: inline-block;
}

.met_audio_progress_bar{
	height: 5px;
	background-color: #8A949F;
	width: auto;
	margin: 0 110px;
	display: block;
	margin-top: -28px;
	cursor: pointer;
}

.met_audio_current_progress{
	background-color: #18ADB6;
	height: 5px;
	position: relative;
	-webkit-transition: width linear 0.1s;
	-moz-transition: width linear 0.1s;
	-ms-transition: width linear 0.1s;
	-o-transition: width linear 0.1s;
}

.met_audio_current_progress:before{
	position: absolute;
	content: '\f0d7';
	font-size: 16px;
	top: -14px;
	right: -3.9px;
	font-family: FontAwesome;
	color: #575757;
}

.met_audio_current_progress:after{
	position: absolute;
	content: '\f0d8';
	font-size: 16px;
	bottom: -14px;
	right: -3.9px;
	font-family: FontAwesome;
	color: #575757;
}

.met_audio_total_time{
	width: 45px;
	height: 50px;
	line-height: 50px;
	text-align: left;
	font-size: 12px;
	color: #FFFFFF;
	display: inline-block;
	float: right;
}

.met_audio_sound{
	width: 50px;
	height: 50px;
	line-height: 50px;
	border: none;
	border-left: 1px solid #FFFFFF;
	color: #FFFFFF;
	text-align: center;
	font-size: 25px;
	background: none;
	float: right;
	position: relative;
	z-index: 3;
	-webkit-transition: all ease-in 0.3s;
	-moz-transition: all ease-in 0.3s;
	-ms-transition: all ease-in 0.3s;
	-o-transition: all ease-in 0.3s;
}

.met_audio_sound:hover{
	background-color: #18ADB6;
}

.met_audio_volume{
	position: absolute;
	height: 50px;
	width: 0;
	top: 0;
	right: 100%;
	overflow: hidden;
	background-color: #18ADB6;
	-webkit-transition: width ease-in 0.3s, padding ease-in 0.3s;
	-moz-transition: width ease-in 0.3s, padding ease-in 0.3s;
	-ms-transition: width ease-in 0.3s, padding ease-in 0.3s;
	-o-transition: width ease-in 0.3s, padding ease-in 0.3s;
}

.met_audio_sound:hover .met_audio_volume{
	width: 100px;
	padding: 0 15px;
}

.met_audio_sound .met_audio_sound_volume{
	position: absolute;
	line-height: 50px;
	top: 0;
	left: 0;
	width: 100%;
	display: none;
}


/*-------------------------------
	Message Boxes
------------------------------*/
.met_message{
	position: relative;
	display: block;
	font-size: 24px;
	font-weight: 600;
	color: #FFFFFF;
	padding: 20px;
}

.met_message:after{
	content: '';
	display: block;
	position: absolute;
	bottom: -16px;
	left: 20px;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 0 16px 16px 0;
}

.met_message_error{background-color: #B61857}.met_message_error:after{border-color: transparent #B61857 transparent transparent;}
.met_message_success{background-color: #18B632}.met_message_success:after{border-color: transparent #18B632 transparent transparent;}
.met_message_info{background-color: #18ADB6}.met_message_info:after{border-color: transparent #18ADB6 transparent transparent;}
.met_message_warning{background-color: #656870}.met_message_warning:after{border-color: transparent #656870 transparent transparent;}




/*-------------------------------
	Progress Bars
------------------------------*/
.progress,
.progress .bar{
	border-radius: 30px;
}

.progress{
	background-color: #C7D6E1;
	box-shadow: inset 0 0 3px 0 #91AFC3;
	height: 30px;
}

.progress .bar{
	line-height: 30px;
	font-size: 12px;
	text-align: right;
}


/*-------------------------------
	Pagination
------------------------------*/
.met_pagination a{
	text-align: center;
	width: 40px;
	height: 40px;
	line-height: 40px;
	color: #FFFFFF;
	background-color: #A4AEB9;
	font-size: 12px;
	margin-right: 2px;
	display: block;
	float: left;
	text-decoration: none;
	position: relative;
	font-weight: 600;
}

.met_pagination a:last-child{
	margin-right: 0;
}

.met_pagination a:before{
	font-family: FontAwesome;
	position: absolute;
	display: block;
	left: 17px;
	top: 0;
	color: #FFFFFF;
	font-size: 12px;
	font-weight: 400;
}

.met_pagination_prev:before{content: '\F100';}
.met_pagination_next:before{content: '\F101';}



/*-------------------------------
	Tab Content
------------------------------*/
.met_tab_nav {
	margin-bottom : 0;
	border-bottom : none;
}

.met_tab_nav > li > a {
	border         : none;
	color          : #FFFFFF;
	font-size      : 18px;
	padding: 27px 40px;
	font-weight: 600;
	-webkit-transition : all ease 0.3s;
	-moz-transition    : all ease 0.3s;
	-ms-transition     : all ease 0.3s;
	-o-transition      : all ease 0.3s;
}

.met_tab_nav > li.active > a {
	border-radius    : 0;
	border           : none;
	background-color : #F8F9FB;
	color: #65676F;
}

.met_tab_nav > li.active > a:hover {
	border           : none;
	background-color : #F8F9FB;
}

.met_tab_content {
	background : #F8F9FB;
	padding    : 15px;
}

.nav > li > a:hover, .nav > li > a:focus{
	background-color: #F8F9FB;
	color: #65676F;
}

.nav-tabs > li > a{
	border-radius: 0;
}



/*-------------------------------
	Gallery
------------------------------*/
.met_gallery .span3{
	margin-bottom : 30px;
}

.met_gallery .span3:nth-child(4n + 1),
.nth-child-2np1 {
	margin-left : 0 !important;
	clear       : both;
}

.met_gallery_wrap a,
.met_gallery_wrap a img{
	display: block;
	float: left;
}

.met_gallery_wrap{
	position: relative;
}

.met_gallery_wrap > a {
	position: relative;
	z-index: 1;
}

.met_gallery_overlay {
	z-index: 2;
	position           : absolute;
	left               : 0;
	top                : 0;
	width              : 100%;
	height             : 100%;
	opacity            : 0;
	visibility         : hidden;
	filter             : alpha(opacity=0);
	-ms-filter         : "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-webkit-transition : all ease 0.5s;
	-moz-transition    : all ease 0.5s;
	-ms-transition     : all ease 0.5s;
	-o-transition      : all ease 0.5s;
	text-align         : center;
	overflow           : hidden;
}

.met_gallery_overlay a {
	position           : absolute;
	display            : inline-block;
	width              : 50px;
	height             : 50px;
	line-height        : 50px;
	text-align         : center;
	text-decoration    : none;
	-webkit-transition : all ease 0.5s;
	-moz-transition    : all ease 0.5s;
	-ms-transition     : all ease 0.5s;
	-o-transition      : all ease 0.5s;
}

.met_gallery_overlay a {
	top  : 100%;
	left : 41%;
}

.met_gallery_wrap:hover .met_gallery_overlay {
	opacity    : 1;
	visibility : visible;
	filter     : alpha(opacity=100);
	-ms-filter : "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
}


.met_gallery_wrap:hover .met_gallery_overlay a {
	top : 42%;
}



/*-------------------------------
	External Contact Form
------------------------------*/
.met_external_contact_form{
	display: block;
}

.met_external_contact_form input,
.met_external_contact_form textarea{
	box-sizing: border-box;
	display: block;
	height: 12px;
	padding: 19px;
	width: 100%;
	border-radius: 0;
	font-size: 11px;
	line-height: 12px;
}

.met_external_contact_form textarea{
	height: 150px;
}


/*-------------------------------
	Response For Lower Than 1170
------------------------------*/
@media all and (max-width: 1170px) {

	.met_page_wrapper.met_boxed_layout{
		width: auto;
	}

	.met_content {
		width        : 90%;
		margin-left  : 5%;
		margin-right : 5%;
	}

	.met_content .row-fluid [class*="span"] {
		margin-bottom : 40px;
	}

	.met_content .row-fluid [class*="span"]:last-child {
		margin-bottom : 0;
	}

	.met_thumbnail_slider_1_small{
		position: relative;
		bottom: 0;
		left: 0;
		height: auto;
	}

	.met_thumbnail_slider_1_small img:first-child{
		margin-left: 0;
	}

	.met_thumbnail_slider_1_small img,
	.met_thumbnail_slider_1_next {
		width: 50px;
		height: 50px;
		padding: 3px;
		margin: 5px 0 0 5px;
	}

	.met_thumbnail_slider_1_next{
		font-size: 70px;
		line-height: 38px;
	}

	.met_thumbnail_slider_1_title a{
		line-height: 41px;
	}

	.met_thumbnail_slider_1_effects_left .met_thumbnail_slider_1_title {
		padding: 10px 20px 10px 40px;
	}

	.met_thumbnail_slider_1_title{
		font-size: 15px;
	}

	.met_thumbnail_slider_1_effects_left .met_thumbnail_slider_1_subtitle {
		padding: 10px 20px 10px 40px;
		left: 0;
	}

	.met_thumbnail_slider_1_subtitle{
		font-size: 13px;
	}

	.met_thumbnail_slider_1_effects_right .met_thumbnail_slider_1_title {
		padding: 10px 40px 10px 20px;
	}

	.met_thumbnail_slider_1_effects_right .met_thumbnail_slider_1_subtitle {
		 padding: 10px 40px 10px 20px;
		 right: 0;
	 }
}


/*-------------------------------
	Response For Tablets
------------------------------*/
@media all and (max-width: 1169px) {

	.met_div5{
		width: 100%;
	}

	.met_footer_menu {
		float : none;
	}

	.met_slider_item {
		overflow : hidden;
	}

	.met_main_nav {
		display : none!important;
	}

	header aside {
		display : none;
	}

	.met_slider_item_preview {
		width : 100%;
	}

	.met_slider_item_caption {
		width       : 100%;
		margin-left : 0;
		position    : relative;
		height      : auto;
		float       : left;
	}

	.met_page_header h1 {
		width   : auto;
		display : block;
	}

	.met_page_header h2:before {
		display : none;
	}

	.met_logo {
		position : relative;
		z-index  : 2;
	}

	.dl-menuwrapper {
		z-index : 1;
	}

	.col-md-4{ width: 20%; float: left; padding: 10px }
	
	.pad div {
		padding:0px!important;
	}
}

/*-------------------------------
	Response For Mobile
------------------------------*/
@media all and (max-width: 767px) {
	.met_img_with_text_preview {
		width : 100%;
	}

	.met_img_with_text article {
		position    : relative;
		margin-left : 0;
		float       : left;
	}

	.met_img_with_text article:before,
	.met_cacoon_sidebar:before {
		display : none;
	}

	.met_icon_tabs nav a {
		width : 56px;
	}

	.met_page_header h2 {
		line-height : 20px;
		height      : auto;
		padding     : 15px 30px;
		font-size   : 15px;
	}

	.met_page_header ul {
		float   : left;
		margin  : 0;
		padding : 15px 30px;
	}

	.met_blog_list_preview aside {
		position : relative;
		width    : auto;
	}

	.met_blog_list_preview aside:after {
		display : none;
	}

	.met_blog_list_preview .met_blog_video_iframe {
		margin-left : 0;
	}

	.met_blog_list_preview .met_blog_video_iframe iframe {
		width  : 100% !important;
		height : 130px !important;
	}

	.met_comment_reply_link {
		display : none;
	}

	.met_comment_box > .met_comment > .met_comment_descr {
		margin-right : 0;
	}

	.met_comment_box > .met_comment:nth-child(2),
	.met_comment_box > .met_comment:nth-child(3),
	.met_comment_box > .met_comment:nth-child(4) {
		margin-left : 0;
	}

	.met_comment_box > .met_sub_comment:before {
		display : none;
	}

	.met_footer_menu {
		display : none;
	}

	.met_responsive_nav {
		display : block;
	}

	.met_recent_work_overbox a {
		top : 4px;
	}

	.met_blog_list_preview aside div {
		padding    : 0;
		text-align : center;
	}

	.met_blog_list_preview aside span:first-child {
		font-size   : 25px;
		margin-top  : 0;
		line-height : 28px;
		display     : inline;
	}

	.met_blog_list_preview aside span:last-child {
		font-size   : 16px;
		line-height : 19px;
		display     : inline;
	}

	.met_blog_posted_by {
		width        : 100%;
		padding-left : 0;
		margin-left  : 0;
		border-left  : none;
	}

	.met_blog_posted_by {
		width        : 100%;
		padding-left : 0;
		margin-left  : 0;
		border-left  : none;
	}

	.met_comment_box > .met_comment > img {
		margin-left   : 80px;
		margin-bottom : 20px;
	}

	.met_comment_box > .met_comment > .met_comment_descr {
		margin  : 0;
		padding : 15px;
	}

	.met_portfolio_item_descr {
		width : 68%;
	}

	.met_portfolio_item_share {
		width : 32%;
	}

	.met_page_header h1 {
		font-size : 20px;
	}

    .met_thumbnail_slider_1_effects{
		display: none;
    }
}

@media all and (max-width: 440px){

	.met_logo{
		width: 100%;
		text-align: center;
	}

	.dl-menuwrapper {
		top: 100px;
		max-width: 100%;
	}

	.dl-menuwrapper button {
		width: 100%;
		text-indent: 1px;
		font-size: 25px;
		font-family: 'Open Sans';
		font-weight: 300;
		color: #FFF;
		text-align: left;
	}

	header.met_content{
		margin-top: 15px;
		margin-bottom: 50px;
	}

	.dl-menuwrapper button:after {
		width: auto;
		height: auto;
		box-shadow: none;
		left: auto;
		top: 0;
		right: 10px;
		background: none;
		color: #FFFFFF;
		font-family: FontAwesome;
		content: '\F0C9';
		font-size: 28px;
		line-height: 41px;
	}

}

.col-md-4{ width: 20%; float: left; padding: 10px }
.center-flex{
	display: flex;
  align-items: center;
  justify-content: center;
}
.pad div{
	padding: 20px
}

.blink_me {
	animation: blinker 2.4s linear infinite;
	color: #0f6b70;
}

@keyframes blinker {
	50% {
		opacity: 0;
	}
}

.keynote-img {
	width: 30%;
	float: left;
	padding-right: 20px;
	padding-bottom: 20px;
}
@media (max-width: 800px) {
	.keynote-img {
		width: 100%;
	}
}
.mobile-pdf {
	height: 50vw;
}

@media (max-width: 1024px) {
	.mobile-pdf {
		height: 100vw;
	}
}

.inst-logo {
	width: 25%!important;
    /*height: auto!important;*/
}

.inst-logo-2{
	width: 100%!important;
    height: auto!important;
}

@media (max-width: 576px) {
	h2.met_bold_one.met_color_transition {
		text-align: center;
	}
	h2.met_bold_one,
	h3.met_bold_one {
		text-align: center;
		font-size: 20.75px;
	}
	h2.met_bold_two.met_color_transition {
		text-align: center;
	}
	h2.met_bold_two,
	h3.met_bold_two {
		text-align: center;
		font-size: 16.75px;
	}
	a.btn.btn-large.btn-primary{
		line-height: 20px;
	}
	.table th {
		padding: 5px;
		font-size: 12px;
	}
	table.table.minus-margin{
		margin-left: -18px;
	}

	.institutions-order .met_testimonial_photos {
		display: flex;
    	flex-direction: column;
    	align-items: center;
    	row-gap: 20px;
	}

	.inst-logo {
		width: 60% !important;
	}
}
table.post-conference tr:nth-child(odd) {
	background-color: lightyellow;
}
.conference-qr{
	padding: 5px;
}
@media (min-width: 1024px) {
	.conference-qr{
		float: right;
		padding-left: 20px;
	}
}
.mt-2{
	margin-top:1rem;
}

