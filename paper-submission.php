<script src="https://cdn.tailwindcss.com"></script>
<style> 
h1 {
    font-size: 30.25px;
}
</style>

<script>

    const DOC_FORM_URL = '/api/v1/save-doc-form-content';

    function doctorForm() {
        return {
            formDoctorData: {
                'full_name': '',
                'email': ''
            },
            files: {
                'submissionDoc': ''
            },
            submitDisabled: false,
            formMessage: '',
            formDetail: '',
            submitDoctorForm() {
                this.submitDisabled = true
                const formDoctorData = new FormData()
                formDoctorData.append('full_name', this.formDoctorData.full_name)
                formDoctorData.append('email', this.formDoctorData.email)
                formDoctorData.append('submissionDoc', this.files.submissionDoc)
                fetch(DOC_FORM_URL, {
                    method: 'POST',
                    headers: {
                        // "Content-Type": "application/json",
                        // Accept: "application/json",
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    },
                    body: formDoctorData
                })
                    .then((response) => response.json())
                    .then((res) => {

                        // empty formData values
                        Object.keys(this.formDoctorData).forEach((value) => {
                            delete this.formDoctorData[value]
                        })

                        // empty files
                        Object.keys(this.files).forEach((value) => {
                            delete this.files[value]
                        })

                        // set message
                        this.formMessage = res.message
                        this.submitDisabled = false

                        if (this.formMessage === 'error') {
                            this.formMessage = res.message
                            this.formDetail = res.details

                            this.submitDisabled = false
                        }
                    })
                    .catch((res) => {
                        this.submitDisabled = false
                    })
            }
        }
    }

</script>

<div class="met_content">
	<div class="row-fluid">
		<div class="span12">
			<div class="met_page_header met_bgcolor5 clearfix">
				<h1 class="met_bgcolor met_color2">Online Submission</h1>
				<h2 class="met_color2">&nbsp;</h2>
				<!-- (3.1. Full Paper Sample, 3.2. Submit Your Paper -->
			</div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span9">
			<div class="row-fluid">
				<div class="span12">
					<div class="met_text_block">

						<!-- <p>Papers to be formatted in accordance with the attached EpSBS-Template-2023-ISMC</p>
						<p>should be submitted to the Conference Secretariat: <a href="mailto:<EMAIL>"><EMAIL></a></p>

						<div class="" style="font-size:14px;line-height:21px;background-color:#F1F4F7!important;color:#65676F!important;padding: 30">
							<p>
								<a class="btn btn-large btn-primary" href="doc/EP-Manuscript_Submission_Manual_v4(EN)2023.pdf" target="_blank">EP Manuscript Submission Manual</a> 
							</p>
							<div class="clearfix"></div>
						</div> -->


						<p>Please see extendend paper sample for guidance.</p>

						<div class="" style="font-size:14px;line-height:21px;background-color:#F1F4F7!important;color:#65676F!important;padding-top:15px;">
							<p>
								<a class="btn btn-large btn-primary" href="doc/template-manuscript-guidelines-for-full-paper-2025.docx" target="_blank">Template 2025 ISMC</a>
							</p>
							<div class="clearfix"></div>
						</div>
                        <div x-data="doctorForm()" class="mt-10">
                            <template x-if="formMessage=='ok'">
                                <div class="alert alert-success" role="alert">
                                    Your full paper has been received successfully.
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            </template>
                            <template x-if="formMessage=='error'">
                                <div class="alert alert-warning" role="alert">
                                    <template x-for="items in formDetail">
                                        <template x-for="item in items">
                                            <span x-text="item"></span>
                                        </template>
                                    </template>
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            </template>
                            <form @submit.prevent="submitDoctorForm" method="post" style="text-align: left;" id="m-form3">
                                <div>
                                    <label class="block">
                                        <span class="text-gray-700">E-Mail</span>
                                        <input type="email" style="color:black; width: 100%; padding: 5px 12px 5px 12px; font-size: 18px; height: 35px; border: 2px solid #1aadb5;" placeholder="E-Mail" required="" x-model="formDoctorData.email">
                                    </label>
                                    <label class="block">
                                        <span class="text-gray-700">Full Name</span>
                                        <input type="text" style="color:black; width: 100%; padding: 5px 12px 5px 12px; font-size: 18px; height: 35px; border: 2px solid #1aadb5;" placeholder="Full Name" required="" x-model="formDoctorData.full_name">
                                    </label>

<!--                                    <div class="mt-0 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-3 pb-6">-->
<!--                                        <div class="space-y-1 text-center">-->
<!--                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">-->
<!--                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>-->
<!--                                            </svg>-->
<!--                                            <div class="flex text-sm text-gray-600">-->
<!--                                                <label for="file-upload-submissionDoc" class="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:text-indigo-500">-->
<!--                                                    <span class="text-base px-2">Add Document</span>-->
<!--                                                    <input id="file-upload-submissionDoc" accept=".doc,.docx,.pdf" x-ref="submissionDoc" type="file" class="sr-only" @change="files.submissionDoc = $refs.submissionDoc.files[0]" required>-->
<!--                                                </label>-->
<!--                                                <p class="pl-1">or Drag & Drop</p>-->
<!--                                            </div>-->
<!--                                            <p class="text-xs text-gray-500">Just .doc & .docx</p>-->
<!--                                        </div>-->
<!--                                    </div>-->
                                    <div class="form-group file-area">
                                        <input id="file-upload-submissionDoc" accept=".doc,.docx,.pdf" x-ref="submissionDoc" type="file" @change="files.submissionDoc = $refs.submissionDoc.files[0]" required/>
                                        <div class="file-dummy">
                                            <div class="success">Great, your files are selected. Keep on.</div>
                                            <div class="default">
                                                <div class="space-y-1 text-center">
                                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                    <div class="mx-auto text-sm text-gray-600">
                                                        <p class="pl-1">or Drag & Drop</p>
                                                    </div>
                                                    <p class="text-xs text-gray-500">Just .doc & .docx</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <button type="submit"
                                        class="mt-4 bg-green-700 btn btn-warning group-invalid:opacity-50 flex w-auto mx-auto justify-center rounded-md border border-transparent py-2 px-4 text-base font-medium text-white shadow-sm hover:bg-yesil/75 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        :disabled="submitDisabled">Send
                                </button>
                            </form>
                        </div>
						
					</div>
				</div>
			</div>
		</div>
		<div class="span3">
			<div class="row-fluid">
				<div class="span12">
					<div class="met_blog_categories met_bgcolor3">
					<!-- 	<a href="?p=chairmans-message-or-about-conference">Chairman’s Message</a>
						<a href="?p=main-topics">Main Topics</a> -->
						<a href="?p=paper-submission-31-full-paper-sample-32-submit-your-paper">Online Submission</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script src="http://ajax.aspnetcdn.com/ajax/jquery.validate/1.14.0/jquery.validate.min.js"></script>
<script src="https://noramice.com//assets/js/messages_tr.js"></script>
<script type="text/javascript">
    setTimeout(function() {
        $('#mydiv21').fadeOut('slow');
    }, 6000); // <-- time in milliseconds
    setTimeout(function() {
        $('#mydiv22').fadeOut('slow');
    }, 6000); // <-- time in milliseconds
</script>
<script>
    $('#recaptcha-error').hide();
    $("#contactForm").validate({
        lang:'tr',
        submitHandler: function(form) {
            var response = grecaptcha.getResponse();
            //recaptcha failed validation
            if (response.length == 0) {
                $('#recaptcha-error').text("This field is required.");
                $('#recaptcha-error').show();
                return false;
            }
            //recaptcha passed validation
            else {
                $('#recaptcha-error').hide();
                return true;
            }
        }
    });
</script>
<style>
    .file-area {
        width: 100%;
        position: relative;
    }
    .file-area input[type=file] {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        cursor: pointer;
    }
    .file-area .file-dummy {
        width: 100%;
        padding: 30px;
        background: rgba(255, 255, 255, 0.2);
        border: 2px dashed rgb(156 163 175);
        text-align: center;
        transition: background 0.3s ease-in-out;
    }
    .file-area .file-dummy .success {
        display: none;
    }
    .file-area:hover .file-dummy {
        background: rgba(255, 255, 255, 0.1);
    }
    .file-area input[type=file]:focus + .file-dummy {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline: -webkit-focus-ring-color auto 5px;
    }
    .file-area input[type=file]:valid + .file-dummy {
        border-color: rgb(23 128 61);
        background-color: rgb(223 240 216);
    }
    .file-area input[type=file]:valid + .file-dummy .success {
        display: inline-block;
    }
    .file-area input[type=file]:valid + .file-dummy .default {
        display: none;
    }
</style>